<template>
  <el-card
    class="drill-curve-chart-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>钻进曲线</span>
        <div class="chart-controls">
          <div class="data-source-switch">
            <el-radio-group
              v-model="dataDisplayMode"
              size="small"
              @change="handleDataSourceChange"
            >
              <el-radio-button label="original">
                原始
              </el-radio-button>
              <el-radio-button label="filtered">
                平滑
              </el-radio-button>
              <el-radio-button label="both">
                全部
              </el-radio-button>
            </el-radio-group>
          </div>

          <!-- 卡钻和突进数据切换开关 -->
          <div class="data-toggle-switches">
            <div class="toggle-switch-item">
              <span class="switch-label">卡钻</span>
              <el-switch
                v-model="showStuckData"
                size="small"
                :disabled="isOperating"
                @change="handleStuckDataToggle"
              />
            </div>
            <div class="toggle-switch-item">
              <span class="switch-label">突进</span>
              <el-switch
                v-model="showMutationData"
                size="small"
                :disabled="isOperating"
                @change="handleMutationDataToggle"
              />
            </div>
          </div>

          <div
            v-show="xAxisType === 'dpth'"
            class="depth-step-select"
          >
            <el-select
              v-model="depthStep"
              placeholder="选择步长"
              size="small"
              style="width: 120px"
              clearable
              @change="handleDepthStepChange"
            >
              <el-option
                label="2cm"
                :value="2"
              />
              <el-option
                label="5cm"
                :value="5"
              />
              <el-option
                label="10cm"
                :value="10"
              />
            </el-select>
          </div>
          <el-tabs
            v-model="xAxisType"
            class="axis-tabs"
            @tab-change="handleAxisTypeChange"
          >
            <el-tab-pane
              v-for="item in xAxisOptions"
              :key="item.value"
              :label="item.label"
              :name="item.value"
            />
          </el-tabs>
        </div>
      </div>
    </template>
    <div
      ref="drillCurveChart"
      class="drill-curve-chart"
    />

    <!-- 数值编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="修改数据点"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form
        :model="editForm"
        label-width="120px"
      >
        <el-form-item :label="xAxisType === 'dpth' ? '深度(cm)' : '采集时间'">
          <el-input
            v-model="editForm.xValue"
            disabled
          />
        </el-form-item>
        <el-form-item label="曲线类型">
          <el-tag :type="getCurveTagType(editForm.curveName)">
            {{ editForm.curveName }}
          </el-tag>
        </el-form-item>
        <el-form-item
          label="数值"
          required
        >
          <el-input-number
            v-model="editForm.value"
            :min="0"
            :max="10000"
            :precision="2"
            :step="0.1"
            style="width: 100%"
          />
          <div class="unit-text">
            {{ getCurveUnit(editForm.curveName) }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="handleSaveEdit"
          >确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script lang="ts">
/**
 * 钻进曲线图表卡片组件
 * 核心图表组件，支持多种数据显示模式和交互功能
 * 这是项目中的参考实现，具有完整的功能和良好的性能
 */

import { ref, watch, onMounted, nextTick, onUnmounted, reactive, computed } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { debouncedChartUpdate, debouncedResize } from '@/utils/debounce'

// 钻进数据项类型
interface DrillingDataItem {
  collectionAt: string
  dpth?: number
  advncSpd?: number
  rtnTq?: number
  wtrPrsH?: number
  frcstKn?: number
  rtnSpd?: number
  [key: string]: unknown
}

// 卡钻/突进数据类型
interface StuckMutationData {
  points: Array<{
    x: number
    y: number
    time: string
    depth: number
  }>
}

export default {
  name: 'DrillCurveChartCard',
  props: {
    drillingData: {
      type: Array as () => DrillingDataItem[],
      default: () => []
    },
    filteringData: {
      type: Array as () => DrillingDataItem[],
      default: () => []
    },
    stuckData: {
      type: Object as () => StuckMutationData | null,
      default: () => null
    },
    mutationData: {
      type: Object as () => StuckMutationData | null,
      default: () => null
    },
    filterStuckData: {
      type: Object as () => StuckMutationData | null,
      default: () => null
    },
    filterMutationData: {
      type: Object as () => StuckMutationData | null,
      default: () => null
    }
  },
  emits: ['stuck-toggle', 'mutation-toggle'],
  setup(props, { emit }) {
    const drillCurveChart = ref(null)
    let chartInstance = null
    const xAxisType = ref('dpth') // 默认使用钻进深度作为X轴
    const xAxisOptions = [
      { label: '钻进深度', value: 'dpth' },
      { label: '采集时间', value: 'time' }
    ]
    const dataDisplayMode = ref('original') // 默认显示原始数据：'original'(原始), 'filtered'(平滑), 'both'(全部)
    const depthStep = ref(null) // 选择的步长，默认为null（不过滤）

    // 卡钻和突进数据显示控制
    const showStuckData = ref(false) // 默认关闭卡钻数据显示
    const showMutationData = ref(false) // 默认关闭突进数据显示

    // 操作锁定状态，防止并发操作
    const isOperating = ref(false)

    // 图例选择状态，用于跟踪哪些曲线被隐藏
    const legendSelected = ref({})

    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器

    // 常量定义
    const CHART_CONSTANTS = {
      GRID_COUNT: 5,
      COLORS: {
        NORMAL: '#389E0D',
        STUCK: '#FF4D4F',
        UP_MUTATION: '#52C41A',
        DOWN_MUTATION: '#FF6B35'
      },
      TOLERANCE: {
        DEPTH: 0.1, // 0.1cm
        TIME: 1000   // 1秒
      }
    }

    // 获取基础曲线配置的公共函数
    const getBaseCurveConfigs = (processedData) => {
      return [
        {
          name: '钻进速度 m/min',
          data: processedData.advncSpdData,
          filteredData: processedData.filteredAdvncSpdData,
          color: CHART_CONSTANTS.COLORS.NORMAL,
          index: 0
        },
        {
          name: '旋转扭矩 Nm',
          data: processedData.rtnTqData,
          filteredData: processedData.filteredRtnTqData,
          color: CHART_CONSTANTS.COLORS.NORMAL,
          index: 1
        },
        {
          name: '水压力 MPa',
          data: processedData.wtrPrsHData,
          filteredData: processedData.filteredWtrPrsHData,
          color: CHART_CONSTANTS.COLORS.NORMAL,
          index: 2
        },
        {
          name: '旋转速度 rpm',
          data: processedData.rtnSpdData,
          filteredData: processedData.filteredRtnSpdData,
          color: CHART_CONSTANTS.COLORS.NORMAL,
          index: 3
        },
        {
          name: '推进力 kN',
          data: processedData.frcstKnData,
          filteredData: processedData.filteredFrcstKnData,
          color: CHART_CONSTANTS.COLORS.NORMAL,
          index: 4
        }
      ]
    }

    // 编辑对话框相关状态
    const editDialogVisible = ref(false)
    const editForm = reactive({
      xValue: '',
      value: 0,
      originalValue: 0,
      curveName: '',
      curveIndex: -1,
      dataIndex: -1
    })

    // 本地数据副本，用于编辑修改
    const localDrillingData = ref([])

    // 曲线配置映射 - 使用统一绿色表示正常状态
    // 钻进速度移动到最顶部，其他曲线依次下移
    const curveConfigs = [
      { name: '钻进速度 m/min', field: 'advncSpd', unit: 'm/min', color: '#389E0D' },
      { name: '旋转扭矩 Nm', field: 'rtnTq', unit: 'Nm', color: '#389E0D' },
      { name: '水压力 MPa', field: 'wtrPrsH', unit: 'MPa', color: '#389E0D' },
      { name: '旋转速度 rpm', field: 'rtnSpd', unit: 'rpm', color: '#389E0D' },
      { name: '推进力 kN', field: 'frcstKn', unit: 'kN', color: '#389E0D' }
    ]

    // 根据曲线名称获取单位
    const getCurveUnit = (curveName) => {
      const config = curveConfigs.find(c => c.name === curveName)
      return config ? config.unit : ''
    }

    // 根据曲线名称获取标签类型
    const getCurveTagType = (curveName) => {
      const typeMap = {
        '钻进速度 m/min': 'info',
        '旋转扭矩 Nm': 'primary',
        '水压力 MPa': 'success',
        '旋转速度 rpm': 'danger',
        '推进力 kN': 'warning'
      }
      return typeMap[curveName] || ''
    }

    // 处理点击事件
    const handleChartClick = (params) => {
      if (params.componentType === 'series' && params.seriesType === 'line') {
        const seriesIndex = params.seriesIndex
        const dataIndex = params.dataIndex

        // 修复：根据显示模式判断曲线类型和索引映射
        // 在不同显示模式下，曲线的排列方式不同
        let actualCurveIndex = seriesIndex
        let isOriginalCurve = true

        if (dataDisplayMode.value === 'original') {
          // 原始模式：只有原始数据曲线，seriesIndex直接对应curveConfigs索引
          actualCurveIndex = seriesIndex
          isOriginalCurve = true
        } else if (dataDisplayMode.value === 'filtered') {
          // 平滑模式：只有滤波数据曲线，seriesIndex直接对应curveConfigs索引，但都是滤波数据
          actualCurveIndex = seriesIndex
          isOriginalCurve = false
        } else if (dataDisplayMode.value === 'both') {
          // 全部模式：原始和滤波数据交替排列，需要映射
          actualCurveIndex = seriesIndex % curveConfigs.length
          isOriginalCurve = seriesIndex < curveConfigs.length
        }

        const curveConfig = curveConfigs[actualCurveIndex]

        if (curveConfig && localDrillingData.value[dataIndex] && isOriginalCurve) {
          const dataPoint = localDrillingData.value[dataIndex]

          editForm.xValue = xAxisType.value === 'dpth'
            ? `${dataPoint.dpth} cm`
            : new Date(dataPoint.collectionAt).toLocaleString()
          editForm.value = parseFloat(dataPoint[curveConfig.field]) || 0
          editForm.originalValue = editForm.value
          editForm.curveName = curveConfig.name
          editForm.curveIndex = actualCurveIndex // 使用映射后的索引
          editForm.dataIndex = dataIndex
          editDialogVisible.value = true
        } else if (!isOriginalCurve) {
          // 提示用户不能编辑滤波数据
          ElMessage.warning('滤波数据不可编辑，请切换到"原始"或"全部"模式编辑原始数据')
        }
      }
    }

    // 处理对话框关闭
    const handleDialogClose = () => {
      editDialogVisible.value = false
      // 重置表单
      editForm.xValue = ''
      editForm.value = 0
      editForm.originalValue = 0
      editForm.curveName = ''
      editForm.curveIndex = -1
      editForm.dataIndex = -1
    }

    // 更新单个数据点
    const updateSingleDataPoint = (seriesIndex, dataIndex, newValue) => {
      if (!chartInstance) return

      try {
        // 获取当前图表配置
        const option = chartInstance.getOption()

        // 更新指定系列的指定数据点
        if (option.series && option.series[seriesIndex] && option.series[seriesIndex].data) {
          option.series[seriesIndex].data[dataIndex] = newValue

          // 正确的方式：创建一个新的series数组，只更新指定的系列
          const newSeries = [...option.series]
          newSeries[seriesIndex] = {
            ...newSeries[seriesIndex],
            data: option.series[seriesIndex].data
          }

          // 只更新这个系列的数据，不重新渲染整个图表
          chartInstance.setOption({
            series: newSeries
          }, false) // false 表示不合并配置，直接替换
        }
      } catch (error) {
        console.error('更新单个数据点失败:', error)
        // 如果单点更新失败，回退到全量更新
        nextTick(() => {
          initChart()
        })
      }
    }

    // 保存编辑
    const handleSaveEdit = () => {
      if (editForm.dataIndex >= 0 && editForm.dataIndex < localDrillingData.value.length) {
        const curveConfig = curveConfigs[editForm.curveIndex]
        if (curveConfig) {
          // 更新本地数据
          localDrillingData.value[editForm.dataIndex][curveConfig.field] = editForm.value

          // 只更新图表中的单个数据点（只更新原始数据曲线）
          updateSingleDataPoint(editForm.curveIndex, editForm.dataIndex, editForm.value)

          ElMessage.success('数据修改成功')
          handleDialogClose()
        }
      }
    }

    // 计算图表容器的最佳高度
    const calculateChartHeight = () => {
      if (!drillCurveChart.value) {
        return 750 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = drillCurveChart.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 1. 当父级高度没有设置时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100) {
        return 750 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算图表区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 750 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 处理X轴类型变更
    const handleAxisTypeChange = (tabName) => {
      // 在tab切换时，tabName会自动传入
      xAxisType.value = tabName

      // 当切换到采集时间模式时，清空步长选择
      if (tabName === 'time') {
        depthStep.value = null
      }

      nextTick(() => {
        initChart()
      })
    }

    // 处理数据源切换
    const handleDataSourceChange = () => {
      // 不清空本地数据，保持编辑功能正常
      // 只需要重新初始化图表以应用新的显示逻辑
      nextTick(() => {
        initChart()
      })

      // 当数据显示模式切换时，自动关闭卡钻和突进开关
      // 避免不同模式下的数据混乱，用户需要重新手动开启
      if (showStuckData.value) {
        showStuckData.value = false
        // 通知父组件关闭卡钻算法
        emit('stuck-toggle', {
          enabled: false,
          dataDisplayMode: dataDisplayMode.value,
          isDataModeChange: true // 标识这是数据模式切换触发的
        })
      }

      if (showMutationData.value) {
        showMutationData.value = false
        // 通知父组件关闭突进算法
        emit('mutation-toggle', {
          enabled: false,
          dataDisplayMode: dataDisplayMode.value,
          isDataModeChange: true // 标识这是数据模式切换触发的
        })
      }
    }

    // 处理步长变化
    const handleDepthStepChange = () => {
      // 步长变化时重新初始化图表
      nextTick(() => {
        initChart()
      })
    }

    // 处理卡钻数据切换
    const handleStuckDataToggle = () => {
      // 防止并发操作
      if (isOperating.value) {
        ElMessage.warning('操作进行中，请稍候...')
        return
      }

      isOperating.value = true

      try {
        if (showStuckData.value) {
          // 开启时：发射事件通知父组件调用算法接口，传递当前数据显示模式
          emit('stuck-toggle', {
            enabled: true,
            dataDisplayMode: dataDisplayMode.value
          })

          // 注意：不在这里立即检查数据，等待父组件接口调用完成后，
          // 通过 props 数据变化来触发图表更新
        } else {
          // 关闭时：发射事件通知父组件
          emit('stuck-toggle', {
            enabled: false,
            dataDisplayMode: dataDisplayMode.value
          })

          // 移除卡钻数据系列，需要刷新整个图表
          nextTick(() => {
            initChart()
          })
        }
      } catch (error) {
        console.error('卡钻数据切换失败:', error)
        showStuckData.value = !showStuckData.value // 重置状态
        ElMessage.error('操作失败，请重试')
      } finally {
        isOperating.value = false
      }
    }

    // 处理突进数据切换
    const handleMutationDataToggle = () => {
      // 防止并发操作
      if (isOperating.value) {
        ElMessage.warning('操作进行中，请稍候...')
        return
      }

      isOperating.value = true

      try {
        if (showMutationData.value) {
          // 开启时：发射事件通知父组件调用算法接口，传递当前数据显示模式
          emit('mutation-toggle', {
            enabled: true,
            dataDisplayMode: dataDisplayMode.value
          })

          // 注意：不在这里立即检查数据，等待父组件接口调用完成后，
          // 通过 props 数据变化来触发图表更新
        } else {
          // 关闭时：发射事件通知父组件
          emit('mutation-toggle', {
            enabled: false,
            dataDisplayMode: dataDisplayMode.value
          })

          // 移除突进数据系列，需要刷新整个图表
          nextTick(() => {
            initChart()
          })
        }
      } catch (error) {
        console.error('突进数据切换失败:', error)
        showMutationData.value = !showMutationData.value // 重置状态
        ElMessage.error('操作失败，请重试')
      } finally {
        isOperating.value = false
      }
    }

    // 添加卡钻数据系列（性能优化版本）
    const addStuckDataSeries = () => {
      if (!chartInstance) {
        console.warn('图表未初始化，无法添加卡钻数据')
        return false
      }

      try {
        // 重新处理数据以获取最新的卡钻点索引
        const processedData = processChartData()
        const { stuckPointIndices } = processedData

        if (!stuckPointIndices || stuckPointIndices.size === 0) {
          ElMessage.info('暂无卡钻数据')
          return 'no-data'
        }

        // 获取当前图表配置
        const currentOption = chartInstance.getOption()
        // 过滤掉已存在的卡钻系列，避免重复添加
        const currentSeries = (currentOption.series || []).filter(series =>
          !series.name || !series.name.includes('(卡钻)')
        )

        // 使用公共函数获取曲线配置
        const baseCurves = getBaseCurveConfigs(processedData)

        // 根据显示模式构建曲线配置
        const curves = []
        baseCurves.forEach(baseCurve => {
          if (dataDisplayMode.value === 'original' || dataDisplayMode.value === 'both') {
            curves.push({
              name: baseCurve.name,
              data: baseCurve.data,
              color: baseCurve.color,
              index: baseCurve.index,
              isFiltered: false
            })
          }

          if ((dataDisplayMode.value === 'filtered' || dataDisplayMode.value === 'both') && baseCurve.filteredData && baseCurve.filteredData.length > 0) {
            curves.push({
              name: baseCurve.name + (dataDisplayMode.value === 'both' ? ' (滤波)' : ''),
              data: baseCurve.filteredData,
              color: baseCurve.color,
              index: baseCurve.index,
              isFiltered: true
            })
          }
        })

        // 为每个参数类型（网格）添加卡钻数据系列
        const newStuckSeries = []
        const gridCount = CHART_CONSTANTS.GRID_COUNT

        // 按参数类型分组曲线
        const curvesByType = {}
        curves.forEach(curve => {
          if (!curvesByType[curve.index]) {
            curvesByType[curve.index] = []
          }
          curvesByType[curve.index].push(curve)
        })

        // 为每个参数类型创建卡钻系列
        for (let gridIndex = 0; gridIndex < gridCount; gridIndex++) {
          const gridCurves = curvesByType[gridIndex] || []
          if (gridCurves.length === 0) continue

          gridCurves.forEach(curve => {
            // 在平滑模式下，只处理滤波数据；在原始模式下，只处理原始数据；在全部模式下，处理所有数据
            const shouldProcessCurve = (dataDisplayMode.value === 'filtered' && curve.isFiltered) ||
                                     (dataDisplayMode.value === 'original' && !curve.isFiltered) ||
                                     (dataDisplayMode.value === 'both')

            if (shouldProcessCurve) {
              // 创建卡钻点数据
              const stuckPointsData = curve.data.map((value, index) => {
                return stuckPointIndices.has(index) ? value : null
              })

              // 创建卡钻点的 markPoint 数据
              const markPointData = []
              if (gridIndex === 0 && stuckPointIndices.size > 0) {
                const sortedIndices = Array.from(stuckPointIndices).sort((a, b) => a - b)
                const segmentStartPoints = [sortedIndices[0]]

                for (let i = 1; i < sortedIndices.length; i++) {
                  if (sortedIndices[i] - sortedIndices[i-1] > 1) {
                    segmentStartPoints.push(sortedIndices[i])
                  }
                }

                segmentStartPoints.forEach(index => {
                  const yValue = curve.data[index]
                  if (yValue !== null && yValue !== undefined) {
                    markPointData.push({
                      xAxis: index,
                      yAxis: yValue,
                      name: '卡钻开始',
                      value: yValue
                    })
                  }
                })
              }

              // 创建卡钻系列配置
              const stuckSeries = {
                name: curve.name + ' (卡钻)',
                type: 'line',
                xAxisIndex: gridIndex,
                yAxisIndex: gridIndex,
                data: stuckPointsData,
                showSymbol: true,
                symbol: 'circle',
                symbolSize: 12,
                connectNulls: false,
                lineStyle: {
                  color: CHART_CONSTANTS.COLORS.STUCK,
                  width: 3,
                  type: 'solid'
                },
                itemStyle: {
                  color: CHART_CONSTANTS.COLORS.STUCK,
                  borderColor: '#FFFFFF',
                  borderWidth: 3,
                  shadowBlur: 5,
                  shadowColor: CHART_CONSTANTS.COLORS.STUCK,
                  shadowOffsetX: 0,
                  shadowOffsetY: 0
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 15,
                    shadowColor: CHART_CONSTANTS.COLORS.STUCK,
                    borderWidth: 4
                  }
                },
                tooltip: {
                  formatter: function(params) {
                    return `${curve.name}<br/>卡钻点: ${params.value}<br/>索引: ${params.dataIndex}`
                  }
                },
                areaStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0,
                      color: '#FF4D4F80'
                    }, {
                      offset: 1,
                      color: '#FF4D4F30'
                    }]
                  }
                },
                markPoint: {
                  data: markPointData,
                  symbol: 'pin',
                  symbolSize: [40, 50],
                  itemStyle: {
                    color: '#FF4D4F',
                    borderColor: '#FFFFFF',
                    borderWidth: 2,
                    shadowBlur: 8,
                    shadowColor: 'rgba(255, 77, 79, 0.5)',
                    shadowOffsetY: 2
                  },
                  label: {
                    show: true,
                    position: 'inside',
                    formatter: '卡',
                    color: '#FFFFFF',
                    fontWeight: 'bold',
                    fontSize: 12
                  },
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 12,
                      shadowColor: 'rgba(255, 77, 79, 0.8)'
                    },
                    label: {
                      fontSize: 12
                    }
                  }
                },
                z: 10,
                silent: false
              }

              newStuckSeries.push(stuckSeries)
            }
          })
        }

        // 将新的卡钻系列添加到现有系列中
        const updatedSeries = [...currentSeries, ...newStuckSeries]

        // 只更新系列数据，不重新渲染整个图表
        chartInstance.setOption({
          series: updatedSeries
        }, false)

        return true // 操作成功

      } catch (error) {
        console.error('添加卡钻数据系列失败:', error)
        // 如果优化更新失败，回退到全量更新
        nextTick(() => {
          initChart()
        })
        return false // 操作失败
      }
    }

    // 添加突进数据系列（性能优化版本）
    const addMutationDataSeries = () => {
      if (!chartInstance) {
        console.warn('图表未初始化，无法添加突进数据')
        return false
      }

      try {
        // 重新处理数据以获取最新的突进点索引
        const processedData = processChartData()
        const { upMutationPointIndices, downMutationPointIndices } = processedData

        if ((!upMutationPointIndices || upMutationPointIndices.size === 0) &&
            (!downMutationPointIndices || downMutationPointIndices.size === 0)) {
          ElMessage.info('暂无突进数据')
          return 'no-data'
        }

        // 获取当前图表配置
        const currentOption = chartInstance.getOption()
        // 过滤掉已存在的突进系列，避免重复添加
        const currentSeries = (currentOption.series || []).filter(series =>
          !series.name || (!series.name.includes('(向上突变)') && !series.name.includes('(向下突变)'))
        )

        // 使用公共函数获取曲线配置
        const baseCurves = getBaseCurveConfigs(processedData)

        // 根据显示模式构建曲线配置
        const curves = []
        baseCurves.forEach(baseCurve => {
          if (dataDisplayMode.value === 'original' || dataDisplayMode.value === 'both') {
            curves.push({
              name: baseCurve.name,
              data: baseCurve.data,
              color: baseCurve.color,
              index: baseCurve.index,
              isFiltered: false
            })
          }

          if ((dataDisplayMode.value === 'filtered' || dataDisplayMode.value === 'both') && baseCurve.filteredData && baseCurve.filteredData.length > 0) {
            curves.push({
              name: baseCurve.name + (dataDisplayMode.value === 'both' ? ' (滤波)' : ''),
              data: baseCurve.filteredData,
              color: baseCurve.color,
              index: baseCurve.index,
              isFiltered: true
            })
          }
        })

        // 为每个参数类型（网格）添加突进数据系列
        const newMutationSeries = []
        const gridCount = CHART_CONSTANTS.GRID_COUNT

        // 按参数类型分组曲线
        const curvesByType = {}
        curves.forEach(curve => {
          if (!curvesByType[curve.index]) {
            curvesByType[curve.index] = []
          }
          curvesByType[curve.index].push(curve)
        })

        // 为每个参数类型创建突进系列
        for (let gridIndex = 0; gridIndex < gridCount; gridIndex++) {
          const gridCurves = curvesByType[gridIndex] || []
          if (gridCurves.length === 0) continue

          gridCurves.forEach(curve => {
            // 在平滑模式下，只处理滤波数据；在原始模式下，只处理原始数据；在全部模式下，处理所有数据
            const shouldProcessCurve = (dataDisplayMode.value === 'filtered' && curve.isFiltered) ||
                                     (dataDisplayMode.value === 'original' && !curve.isFiltered) ||
                                     (dataDisplayMode.value === 'both')

            if (shouldProcessCurve) {
              // 添加向上突变点系列
              if (upMutationPointIndices && upMutationPointIndices.size > 0) {
                const upMutationPointsData = curve.data.map((value, index) => {
                  return upMutationPointIndices.has(index) ? value : null
                })

                // 创建向上突变点的 markPoint 数据
                const upMutationMarkPointData = []
                if (gridIndex === 0 && upMutationPointIndices.size > 0) {
                  const sortedUpMutationIndices = Array.from(upMutationPointIndices).sort((a, b) => a - b)
                  const upMutationSegmentStartPoints = [sortedUpMutationIndices[0]]

                  for (let i = 1; i < sortedUpMutationIndices.length; i++) {
                    if (sortedUpMutationIndices[i] - sortedUpMutationIndices[i-1] > 1) {
                      upMutationSegmentStartPoints.push(sortedUpMutationIndices[i])
                    }
                  }

                  upMutationSegmentStartPoints.forEach(index => {
                    const yValue = curve.data[index]
                    if (yValue !== null && yValue !== undefined) {
                      upMutationMarkPointData.push({
                        xAxis: index,
                        yAxis: yValue,
                        name: '向上突变开始',
                        value: yValue
                      })
                    }
                  })
                }

                // 创建向上突变系列配置
                const upMutationSeries = {
                  name: curve.name + ' (向上突变)',
                  type: 'line',
                  xAxisIndex: gridIndex,
                  yAxisIndex: gridIndex,
                  data: upMutationPointsData,
                  showSymbol: true,
                  symbol: 'circle',
                  symbolSize: 12,
                  connectNulls: false,
                  lineStyle: {
                    color: '#52C41A',
                    width: 2,
                    type: 'solid'
                  },
                  itemStyle: {
                    color: '#52C41A',
                    borderColor: '#FFFFFF',
                    borderWidth: 2,
                    shadowBlur: 3,
                    shadowColor: '#52C41A',
                    shadowOffsetX: 0,
                    shadowOffsetY: 0
                  },
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: '#52C41A',
                      borderWidth: 3
                    }
                  },
                  tooltip: {
                    formatter: function(params) {
                      return `${curve.name}<br/>向上突变点: ${params.value}<br/>索引: ${params.dataIndex}`
                    }
                  },
                  areaStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [{
                        offset: 0,
                        color: '#52C41A80'
                      }, {
                        offset: 1,
                        color: '#52C41A30'
                      }]
                    }
                  },
                  markPoint: {
                    data: upMutationMarkPointData,
                    symbol: 'pin',
                    symbolSize: [40, 50],
                    itemStyle: {
                      color: '#52C41A',
                      borderColor: '#FFFFFF',
                      borderWidth: 2,
                      shadowBlur: 8,
                      shadowColor: 'rgba(82, 196, 26, 0.5)',
                      shadowOffsetY: 2
                    },
                    label: {
                      show: true,
                      position: 'inside',
                      formatter: '↑',
                      color: '#FFFFFF',
                      fontWeight: '900',
                      fontSize: 20
                    },
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 12,
                        shadowColor: 'rgba(82, 196, 26, 0.8)'
                      },
                      label: {
                        fontSize: 22
                      }
                    }
                  },
                  z: 9,
                  silent: false
                }

                newMutationSeries.push(upMutationSeries)
              }

              // 添加向下突变点系列
              if (downMutationPointIndices && downMutationPointIndices.size > 0) {
                const downMutationPointsData = curve.data.map((value, index) => {
                  return downMutationPointIndices.has(index) ? value : null
                })

                // 创建向下突变点的 markPoint 数据
                const downMutationMarkPointData = []
                if (gridIndex === 0 && downMutationPointIndices.size > 0) {
                  const sortedDownMutationIndices = Array.from(downMutationPointIndices).sort((a, b) => a - b)
                  const downMutationSegmentStartPoints = [sortedDownMutationIndices[0]]

                  for (let i = 1; i < sortedDownMutationIndices.length; i++) {
                    if (sortedDownMutationIndices[i] - sortedDownMutationIndices[i-1] > 1) {
                      downMutationSegmentStartPoints.push(sortedDownMutationIndices[i])
                    }
                  }

                  downMutationSegmentStartPoints.forEach(index => {
                    const yValue = curve.data[index]
                    if (yValue !== null && yValue !== undefined) {
                      downMutationMarkPointData.push({
                        xAxis: index,
                        yAxis: yValue,
                        name: '向下突变开始',
                        value: yValue
                      })
                    }
                  })
                }

                // 创建向下突变系列配置
                const downMutationSeries = {
                  name: curve.name + ' (向下突变)',
                  type: 'line',
                  xAxisIndex: gridIndex,
                  yAxisIndex: gridIndex,
                  data: downMutationPointsData,
                  showSymbol: true,
                  symbol: 'circle',
                  symbolSize: 12,
                  connectNulls: false,
                  lineStyle: {
                    color: '#FF6B35',
                    width: 2,
                    type: 'solid'
                  },
                  itemStyle: {
                    color: '#FF6B35',
                    borderColor: '#FFFFFF',
                    borderWidth: 2,
                    shadowBlur: 3,
                    shadowColor: '#FF6B35',
                    shadowOffsetX: 0,
                    shadowOffsetY: 0
                  },
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: '#FF6B35',
                      borderWidth: 3
                    }
                  },
                  tooltip: {
                    formatter: function(params) {
                      return `${curve.name}<br/>向下突变点: ${params.value}<br/>索引: ${params.dataIndex}`
                    }
                  },
                  areaStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [{
                        offset: 0,
                        color: '#FF6B3580'
                      }, {
                        offset: 1,
                        color: '#FF6B3530'
                      }]
                    }
                  },
                  markPoint: {
                    data: downMutationMarkPointData,
                    symbol: 'pin',
                    symbolSize: [40, 50],
                    itemStyle: {
                      color: '#FF6B35',
                      borderColor: '#FFFFFF',
                      borderWidth: 2,
                      shadowBlur: 8,
                      shadowColor: 'rgba(255, 107, 53, 0.5)',
                      shadowOffsetY: 2
                    },
                    label: {
                      show: true,
                      position: 'inside',
                      formatter: '↓',
                      color: '#FFFFFF',
                      fontWeight: '900',
                      fontSize: 20
                    },
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 12,
                        shadowColor: 'rgba(255, 107, 53, 0.8)'
                      },
                      label: {
                        fontSize: 22
                      }
                    }
                  },
                  z: 8,
                  silent: false
                }

                newMutationSeries.push(downMutationSeries)
              }
            }
          })
        }

        // 将新的突进系列添加到现有系列中
        const updatedSeries = [...currentSeries, ...newMutationSeries]

        // 只更新系列数据，不重新渲染整个图表
        chartInstance.setOption({
          series: updatedSeries
        }, false)

        return true // 操作成功

      } catch (error) {
        console.error('添加突进数据系列失败:', error)
        // 如果优化更新失败，回退到全量更新
        nextTick(() => {
          initChart()
        })
        return false // 操作失败
      }
    }

    // 根据步长过滤数据
    const filterDataByDepthStep = (data, step) => {
      if (!data || data.length === 0 || !step) {
        return data
      }

      // 按钻进深度进行步长过滤和去重
      const depthMap = new Map() // 用于存储每个深度区间的数据

      // 对数据按深度排序
      const sortedData = [...data].sort((a, b) => {
        const depthA = parseFloat(a.dpth) || 0
        const depthB = parseFloat(b.dpth) || 0
        return depthA - depthB
      })

      for (const item of sortedData) {
        const currentDepth = parseFloat(item.dpth) || 0

        // 计算当前深度所属的步长区间
        const depthInterval = Math.floor(currentDepth / step) * step

        // 如果这个深度区间还没有数据，或者当前数据的时间更新，则使用当前数据
        if (!depthMap.has(depthInterval)) {
          depthMap.set(depthInterval, item)
        } else {
          // 如果已有数据，比较时间戳，保留最新的数据
          const existingItem = depthMap.get(depthInterval)
          const currentTime = new Date(item.collectionAt).getTime()
          const existingTime = new Date(existingItem.collectionAt).getTime()

          if (currentTime > existingTime) {
            depthMap.set(depthInterval, item)
          }
        }
      }

      // 将Map中的数据转换为数组，并按深度排序
      const result = Array.from(depthMap.values()).sort((a, b) => {
        const depthA = parseFloat(a.dpth) || 0
        const depthB = parseFloat(b.dpth) || 0
        return depthA - depthB
      })

      return result
    }

    // 初始化图表
    const initChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
      }

      // 确保DOM元素存在再初始化
      if (!drillCurveChart.value) {
        console.warn('钻进曲线图表DOM元素不存在')
        return
      }

      // 动态计算并设置图表高度
      const chartHeight = calculateChartHeight()
      lastCalculatedHeight = chartHeight // 记录初始高度
      drillCurveChart.value.style.height = chartHeight + 'px'

      chartInstance = echarts.init(drillCurveChart.value)

      // 处理数据，为图表做准备
      const processedData = processChartData()
      
      // 设置图表选项
      const option = createChartOption(processedData)

      chartInstance.setOption(option)

      // 初始化图例选择状态
      const initialLegendSelected = {}
      option.legend.data.forEach(name => {
        initialLegendSelected[name] = true
      })
      legendSelected.value = initialLegendSelected

      // 添加点击事件监听
      chartInstance.on('click', handleChartClick)

      // 添加图例选择变化事件监听
      chartInstance.on('legendselectchanged', (params) => {
        legendSelected.value = { ...params.selected }
        // 只更新 xAxis 的 axisPointer 配置，不影响图例状态
        nextTick(() => {
          const processedData = processChartData()
          const option = createChartOption(processedData)
          // 只更新 xAxis 配置，保持图例状态
          chartInstance.setOption({
            xAxis: option.xAxis
          }, false) // 使用合并模式，不替换整个配置
        })
      })
    }

    // 处理数据为图表所需格式
    const processChartData = () => {
      // 简单的数值解析函数
      const parseValidFloat = (value) => {
        if (value === null || value === undefined || value === '') {
          return null;
        }
        const parsed = parseFloat(value);
        return isNaN(parsed) ? null : parsed;
      };

      // 数据验证函数
      const validateDataItem = (item) => {
        if (!item || typeof item !== 'object') return false;

        // 检查必要字段是否存在
        if (xAxisType.value === 'dpth') {
          return item.dpth !== undefined && item.dpth !== null;
        } else {
          return item.collectionAt !== undefined && item.collectionAt !== null;
        }
      };

      // 数据排序函数
      const sortData = (data) => {
        if (!data || data.length === 0) return [];

        // 过滤无效数据
        const validData = data.filter(validateDataItem);
        if (validData.length === 0) return [];

        const sorted = [...validData];
        try {
          if (xAxisType.value === 'dpth') {
            sorted.sort((a, b) => {
              const depthA = parseFloat(a.dpth || 0)
              const depthB = parseFloat(b.dpth || 0)
              return depthA - depthB
            })
          } else {
            sorted.sort((a, b) => {
              const timeA = new Date(a.collectionAt || 0).getTime()
              const timeB = new Date(b.collectionAt || 0).getTime()
              // 检查时间是否有效
              if (isNaN(timeA) || isNaN(timeB)) {
                console.warn('发现无效的时间数据:', a.collectionAt, b.collectionAt);
                return 0;
              }
              return timeA - timeB
            })
          }
        } catch (error) {
          console.error('数据排序失败:', error);
          return validData; // 返回未排序的有效数据
        }
        return sorted;
      };

      // 获取原始数据和滤波数据
      const originalData = props.drillingData || [];
      const filteredData = props.filteringData || [];

      // 确保本地数据与原始数据同步（用于编辑功能）
      if (localDrillingData.value.length === 0 && originalData.length > 0) {
        localDrillingData.value = JSON.parse(JSON.stringify(originalData))
      }

      // 如果没有任何数据，返回空结构
      if (originalData.length === 0 && localDrillingData.value.length === 0) {
        return {
          xAxisData: [],
          rtnTqData: [],
          wtrPrsHData: [],
          rtnSpdData: [],
          frcstKnData: [],
          advncSpdData: [],
          filteredRtnTqData: [],
          filteredWtrPrsHData: [],
          filteredRtnSpdData: [],
          filteredFrcstKnData: [],
          filteredAdvncSpdData: []
        }
      }

      // 处理原始数据（优先使用本地数据，支持编辑功能）
      let originalSortedData = localDrillingData.value.length > 0 ? [...localDrillingData.value] : [...originalData]

      if (depthStep.value && depthStep.value > 0 && originalSortedData.length > 0) {
        originalSortedData = filterDataByDepthStep(originalSortedData, depthStep.value)
      }
      originalSortedData = sortData(originalSortedData);

      // 处理滤波数据（在平滑或全部模式时处理）
      let filteredSortedData = []
      if ((dataDisplayMode.value === 'filtered' || dataDisplayMode.value === 'both') && filteredData.length > 0) {
        filteredSortedData = [...filteredData]
        if (depthStep.value && depthStep.value > 0) {
          filteredSortedData = filterDataByDepthStep(filteredSortedData, depthStep.value)
        }
        filteredSortedData = sortData(filteredSortedData);
      }

      // 使用原始数据的X轴数据（因为编辑功能基于原始数据）
      const xAxisData = originalSortedData.length > 0
        ? originalSortedData.map(item => xAxisType.value === 'dpth' ? item.dpth : item.collectionAt)
        : []

      // 处理原始数据
      const rtnTqData = originalSortedData.map(item => parseValidFloat(item.rtnTq));
      const wtrPrsHData = originalSortedData.map(item => parseValidFloat(item.wtrPrsH));
      const rtnSpdData = originalSortedData.map(item => parseValidFloat(item.rtnSpd));
      const frcstKnData = originalSortedData.map(item => parseValidFloat(item.frcstKn));
      const advncSpdData = originalSortedData.map(item => parseValidFloat(item.advncSpd));

      // 处理滤波数据
      const filteredRtnTqData = filteredSortedData.map(item => parseValidFloat(item.rtnTq));
      const filteredWtrPrsHData = filteredSortedData.map(item => parseValidFloat(item.wtrPrsH));
      const filteredRtnSpdData = filteredSortedData.map(item => parseValidFloat(item.rtnSpd));
      const filteredFrcstKnData = filteredSortedData.map(item => parseValidFloat(item.frcstKn));
      const filteredAdvncSpdData = filteredSortedData.map(item => parseValidFloat(item.advncSpd));

      // 处理卡钻点数据 - 根据数据显示模式选择正确的数据源
      const stuckPointIndices = new Set()
      let stuckPoints = []

      // 根据数据显示模式选择卡钻数据源
      if (dataDisplayMode.value === 'original') {
        stuckPoints = props.stuckData?.stuckPoints || []
      } else if (dataDisplayMode.value === 'filtered') {
        stuckPoints = props.filterStuckData?.stuckPoints || []
      } else if (dataDisplayMode.value === 'both') {
        // 全部模式：优先使用滤波数据，如果没有则使用原始数据
        stuckPoints = props.filterStuckData?.stuckPoints || props.stuckData?.stuckPoints || []
      }

      if (stuckPoints && Array.isArray(stuckPoints)) {
        // 创建已匹配索引的集合，避免重复匹配
        const usedIndices = new Set()

        // 根据数据显示模式选择正确的数据源进行索引匹配
        const targetData = (dataDisplayMode.value === 'filtered' && filteredSortedData.length > 0)
          ? filteredSortedData
          : originalSortedData

        // 根据时间或深度匹配卡钻点
        stuckPoints.forEach((stuckPoint) => {
          // 在目标数据中查找匹配的索引，排除已使用的索引
          let bestMatch = null
          let bestDiff = Infinity

          targetData.forEach((dataPoint, index) => {
            // 跳过已经匹配的索引
            if (usedIndices.has(index)) return

            let diff = Infinity
            if (xAxisType.value === 'dpth') {
              // 按深度匹配
              const dataDpth = parseFloat(dataPoint.dpth) || 0
              const stuckDpth = parseFloat(stuckPoint.dpth) || 0
              diff = Math.abs(dataDpth - stuckDpth)
            } else {
              // 按时间匹配
              const dataTime = new Date(dataPoint.collectionAt).getTime()
              const stuckTime = new Date(stuckPoint.collectionAt).getTime()
              diff = Math.abs(dataTime - stuckTime)
            }

            // 找到最佳匹配
            if (diff < bestDiff) {
              bestDiff = diff
              bestMatch = index
            }
          })

          // 检查最佳匹配是否在允许的误差范围内
          const tolerance = xAxisType.value === 'dpth' ? 0.1 : 1000 // 0.1cm 或 1秒
          if (bestMatch !== null && bestDiff <= tolerance) {
            usedIndices.add(bestMatch)
            stuckPointIndices.add(bestMatch)
          }
        })
      }

      // 处理向上突变点数据 - 根据数据显示模式选择正确的数据源
      const upMutationPointIndices = new Set()
      let upMutationPoints = []

      // 根据数据显示模式选择突进数据源
      if (dataDisplayMode.value === 'original') {
        upMutationPoints = props.mutationData?.upwardPoints || []
      } else if (dataDisplayMode.value === 'filtered') {
        upMutationPoints = props.filterMutationData?.upwardPoints || []
      } else if (dataDisplayMode.value === 'both') {
        // 全部模式：优先使用滤波数据，如果没有则使用原始数据
        upMutationPoints = props.filterMutationData?.upwardPoints || props.mutationData?.upwardPoints || []
      }

      if (upMutationPoints && Array.isArray(upMutationPoints)) {
        // 创建已匹配索引的集合，避免重复匹配
        const usedUpMutationIndices = new Set()

        // 根据数据显示模式选择正确的数据源进行索引匹配
        const targetData = (dataDisplayMode.value === 'filtered' && filteredSortedData.length > 0)
          ? filteredSortedData
          : originalSortedData

        // 根据时间或深度匹配向上突变点
        upMutationPoints.forEach((mutationPoint) => {
          // 在目标数据中查找匹配的索引，排除已使用的索引
          let bestMatch = null
          let bestDiff = Infinity

          targetData.forEach((dataPoint, index) => {
            // 跳过已经匹配的索引
            if (usedUpMutationIndices.has(index)) return

            let diff = Infinity
            if (xAxisType.value === 'dpth') {
              // 按深度匹配
              const dataDpth = parseFloat(dataPoint.dpth) || 0
              const mutationDpth = parseFloat(mutationPoint.dpth) || 0
              diff = Math.abs(dataDpth - mutationDpth)
            } else {
              // 按时间匹配
              const dataTime = new Date(dataPoint.collectionAt).getTime()
              const mutationTime = new Date(mutationPoint.collectionAt).getTime()
              diff = Math.abs(dataTime - mutationTime)
            }

            // 找到最佳匹配
            if (diff < bestDiff) {
              bestDiff = diff
              bestMatch = index
            }
          })

          // 检查最佳匹配是否在允许的误差范围内
          const tolerance = xAxisType.value === 'dpth' ? 0.1 : 1000 // 0.1cm 或 1秒
          if (bestMatch !== null && bestDiff <= tolerance) {
            usedUpMutationIndices.add(bestMatch)
            upMutationPointIndices.add(bestMatch)
          }
        })
      }

      // 处理向下突变点数据 - 根据数据显示模式选择正确的数据源
      const downMutationPointIndices = new Set()
      let downMutationPoints = []

      // 根据数据显示模式选择突进数据源
      if (dataDisplayMode.value === 'original') {
        downMutationPoints = props.mutationData?.downwardPoints || []
      } else if (dataDisplayMode.value === 'filtered') {
        downMutationPoints = props.filterMutationData?.downwardPoints || []
      } else if (dataDisplayMode.value === 'both') {
        // 全部模式：优先使用滤波数据，如果没有则使用原始数据
        downMutationPoints = props.filterMutationData?.downwardPoints || props.mutationData?.downwardPoints || []
      }

      if (downMutationPoints && Array.isArray(downMutationPoints)) {
        // 创建已匹配索引的集合，避免重复匹配
        const usedDownMutationIndices = new Set()

        // 根据数据显示模式选择正确的数据源进行索引匹配
        const targetData = (dataDisplayMode.value === 'filtered' && filteredSortedData.length > 0)
          ? filteredSortedData
          : originalSortedData

        // 根据时间或深度匹配向下突变点
        downMutationPoints.forEach((mutationPoint) => {
          // 在目标数据中查找匹配的索引，排除已使用的索引
          let bestMatch = null
          let bestDiff = Infinity

          targetData.forEach((dataPoint, index) => {
            // 跳过已经匹配的索引
            if (usedDownMutationIndices.has(index)) return

            let diff = Infinity
            if (xAxisType.value === 'dpth') {
              // 按深度匹配
              const dataDpth = parseFloat(dataPoint.dpth) || 0
              const mutationDpth = parseFloat(mutationPoint.dpth) || 0
              diff = Math.abs(dataDpth - mutationDpth)
            } else {
              // 按时间匹配
              const dataTime = new Date(dataPoint.collectionAt).getTime()
              const mutationTime = new Date(mutationPoint.collectionAt).getTime()
              diff = Math.abs(dataTime - mutationTime)
            }

            // 找到最佳匹配
            if (diff < bestDiff) {
              bestDiff = diff
              bestMatch = index
            }
          })

          // 检查最佳匹配是否在允许的误差范围内
          const tolerance = xAxisType.value === 'dpth' ? 0.1 : 1000 // 0.1cm 或 1秒
          if (bestMatch !== null && bestDiff <= tolerance) {
            usedDownMutationIndices.add(bestMatch)
            downMutationPointIndices.add(bestMatch)
          }
        })
      }

      return {
        xAxisData,
        rtnTqData,
        wtrPrsHData,
        rtnSpdData,
        frcstKnData,
        advncSpdData,
        filteredRtnTqData,
        filteredWtrPrsHData,
        filteredRtnSpdData,
        filteredFrcstKnData,
        filteredAdvncSpdData,
        stuckPointIndices, // 传递卡钻点索引
        upMutationPointIndices, // 传递向上突变点索引
        downMutationPointIndices // 传递向下突变点索引
      }
    }

    // 创建图表选项
    const createChartOption = (data) => {
      const {
        xAxisData,
        rtnTqData,
        wtrPrsHData,
        rtnSpdData,
        frcstKnData,
        advncSpdData,
        filteredRtnTqData,
        filteredWtrPrsHData,
        filteredRtnSpdData,
        filteredFrcstKnData,
        filteredAdvncSpdData,
        stuckPointIndices,
        upMutationPointIndices,
        downMutationPointIndices
      } = data

      // 格式化X轴标签
      const xAxisLabel = xAxisType.value === 'dpth'
        ? { formatter: '{value} cm' }
        : {
            formatter: function(value) {
              const date = new Date(value);
              return date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
            }
          }

      // 使用公共函数获取基础曲线配置
      const baseCurves = getBaseCurveConfigs({
        advncSpdData,
        filteredAdvncSpdData,
        rtnTqData,
        filteredRtnTqData,
        wtrPrsHData,
        filteredWtrPrsHData,
        rtnSpdData,
        filteredRtnSpdData,
        frcstKnData,
        filteredFrcstKnData
      });

      // 根据显示模式构建最终的曲线配置
      const curves = [];
      baseCurves.forEach(baseCurve => {
        // 根据显示模式决定添加哪些曲线
        if (dataDisplayMode.value === 'original' || dataDisplayMode.value === 'both') {
          // 添加原始数据曲线
          curves.push({
            name: baseCurve.name,
            data: baseCurve.data,
            color: baseCurve.color,
            index: baseCurve.index,
            isFiltered: false
          });
        }

        if ((dataDisplayMode.value === 'filtered' || dataDisplayMode.value === 'both') && baseCurve.filteredData && baseCurve.filteredData.length > 0) {
          // 添加滤波数据曲线
          curves.push({
            name: baseCurve.name + (dataDisplayMode.value === 'both' ? ' (滤波)' : ''),
            data: baseCurve.filteredData,
            color: baseCurve.color,
            index: baseCurve.index,
            isFiltered: true
          });
        }
      });

      // 设置网格配置 - 保持5个网格，每个网格对应一种参数类型
      const gridCount = CHART_CONSTANTS.GRID_COUNT
      const gridGap = 30; // 网格之间的间距
      const topMargin = 100; // 顶部边距，为标题和图例留出空间
      const bottomMargin = 70; // 底部边距，为数据缩放控制器留出空间

      // 动态计算每个网格的高度
      const totalHeight = lastCalculatedHeight; // 使用当前图表的实际高度
      const availableGridHeight = totalHeight - topMargin - bottomMargin - gridGap * (gridCount - 1);

      // 确保网格高度
      let gridHeight = Math.floor(availableGridHeight / gridCount);

      // 创建网格、X轴、Y轴和数据系列的空数组
      const grids = [];
      const xAxes = [];
      const yAxes = [];
      const series = [];

      // 按参数类型分组曲线
      const curvesByType = {};
      curves.forEach(curve => {
        if (!curvesByType[curve.index]) {
          curvesByType[curve.index] = [];
        }
        curvesByType[curve.index].push(curve);
      });

      // 为每个参数类型创建网格配置
      for (let gridIndex = 0; gridIndex < gridCount; gridIndex++) {
        const gridCurves = curvesByType[gridIndex] || [];
        if (gridCurves.length === 0) continue;

        // 使用第一条曲线的信息作为网格的基础配置
        const primaryCurve = gridCurves[0];

        // 计算当前网格的位置
        const gridTop = topMargin + gridIndex * (gridHeight + gridGap);

        // 添加网格配置
        grids.push({
          top: gridTop,
          height: gridHeight,
          left: 60,  // 留出Y轴标签的空间
          right: 20
        });

        // 添加X轴配置
        xAxes.push({
          type: 'category',
          gridIndex: gridIndex,
          axisLabel: gridIndex === gridCount - 1 ? xAxisLabel : { show: false }, // 只在最底部网格显示X轴标签
          axisLine: { show: gridIndex === gridCount - 1 }, // 只在最底部网格显示X轴线
          axisTick: { show: gridIndex === gridCount - 1 }, // 只在最底部网格显示X轴刻度
          splitLine: { show: false },
          position: 'bottom',
          data: xAxisData,
          // 为每个X轴配置自定义的axisPointer标签
          axisPointer: {
            label: {
              // 根据当前网格是否有可见曲线来决定是否显示标签
              show: gridCurves.some(curve => legendSelected.value[curve.name] !== false),
              backgroundColor: '#ccc',
              borderColor: '#aaa',
              borderWidth: 1,
              shadowBlur: 0,
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              // 自定义灰色标签显示内容
              formatter: (function(currentCurves, xAxisData, legendSelectedRef) {
                return function(params) {
                  // 首先检查当前网格是否有可见的曲线
                  const visibleCurves = currentCurves.filter(curve => legendSelectedRef.value[curve.name] !== false);

                  // 如果没有可见的曲线，返回空字符串
                  if (visibleCurves.length === 0) {
                    return '';
                  }

                  let dataIndex = -1;

                  // 优先使用 params.seriesData 获取精确的数据索引
                  if (params.seriesData && params.seriesData.length > 0) {
                    // 从seriesData中获取第一个有效的dataIndex
                    for (let i = 0; i < params.seriesData.length; i++) {
                      const seriesItem = params.seriesData[i];
                      if (seriesItem.dataIndex !== undefined && seriesItem.dataIndex >= 0) {
                        dataIndex = seriesItem.dataIndex;
                        break;
                      }
                    }
                  }

                  // 如果seriesData没有提供有效的dataIndex，使用降级策略
                  if (dataIndex === -1) {
                    // 尝试使用 params.dataIndex
                    if (params.dataIndex !== undefined && params.dataIndex >= 0 && params.dataIndex < xAxisData.length) {
                      dataIndex = params.dataIndex;
                    } else {
                      // 最后的降级方案：通过X轴值查找第一个匹配的索引
                      const currentXValue = params.value;
                      for (let i = 0; i < xAxisData.length; i++) {
                        if (String(xAxisData[i]) === String(currentXValue)) {
                          dataIndex = i;
                          break;
                        }
                      }
                    }
                  }

                  if (dataIndex === -1 || dataIndex >= xAxisData.length) {
                    return ''; // 找不到对应数据
                  }

                  const values = visibleCurves.map(curve => {
                    const value = curve.data[dataIndex];
                    const formattedValue = value !== null && value !== undefined ?
                      (typeof value === 'number' ? value.toFixed(2) : value) :
                      '无数据';
                    return `${curve.name}: ${formattedValue}`;
                  });

                  return values.join('\n');
                };
              })(gridCurves, xAxisData, legendSelected)
            }
          }
        });

        // 添加Y轴配置
        yAxes.push({
          type: 'value',
          gridIndex: gridIndex,
          name: primaryCurve.name.replace(' (滤波)', ''), // 移除滤波标识，显示基础名称
          nameLocation: 'middle',
          nameGap: 45,
          nameTextStyle: {
            color: primaryCurve.color,
            fontWeight: 'bold'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: primaryCurve.color
            }
          },
          axisLabel: {
            color: primaryCurve.color,
            formatter: '{value}'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(233, 233, 233, 0.5)',
              type: 'dashed'
            }
          }
        });

        // 为当前网格的所有曲线添加数据系列配置
        gridCurves.forEach(curve => {
          // 创建基础线条系列
          series.push({
            name: curve.name,
            type: 'line',
            xAxisIndex: gridIndex,
            yAxisIndex: gridIndex,
            data: curve.data,
            showSymbol: false,
            connectNulls: true,
            lineStyle: {
              color: curve.color,
              width: curve.isFiltered ? 2 : 2, // 保持相同宽度
              type: curve.isFiltered ? 'dashed' : 'solid' // 滤波数据使用虚线
            },
            itemStyle: {
              color: curve.color
            },
            // 只有原始数据显示面积填充
            areaStyle: curve.isFiltered ? null : {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: curve.color + '40' // 40%透明度
                }, {
                  offset: 1,
                  color: curve.color + '10' // 10%透明度
                }]
              }
            }
          });

          // 为原始数据曲线添加卡钻点标记 - 根据开关状态决定是否显示
          if (!curve.isFiltered && showStuckData.value && stuckPointIndices && stuckPointIndices.size > 0) {
            // 创建卡钻点数据 - 只在卡钻点位置显示数据
            const stuckPointsData = curve.data.map((value, index) => {
              return stuckPointIndices.has(index) ? value : null
            })



            // 创建卡钻点的 markPoint 数据 - 只在第一个网格（最上面的图表）的每段卡钻起始点添加
            const markPointData = []
            if (gridIndex === 0 && stuckPointIndices.size > 0) { // 只在第一个网格添加 markPoint
              // 将卡钻点索引排序
              const sortedIndices = Array.from(stuckPointIndices).sort((a, b) => a - b)

              // 找到每段连续卡钻的起始点
              const segmentStartPoints = [sortedIndices[0]] // 第一个点肯定是起始点

              for (let i = 1; i < sortedIndices.length; i++) {
                // 如果当前索引与前一个索引不连续，说明是新段的开始
                if (sortedIndices[i] - sortedIndices[i-1] > 1) {
                  segmentStartPoints.push(sortedIndices[i])
                }
              }

              // 为每个起始点创建 markPoint
              segmentStartPoints.forEach(index => {
                const yValue = curve.data[index]
                if (yValue !== null && yValue !== undefined) {
                  markPointData.push({
                    xAxis: index, // 使用数据索引作为x坐标
                    yAxis: yValue, // 使用实际数值作为y坐标
                    name: '卡钻开始',
                    value: yValue
                  })
                }
              })
            }

            // 添加卡钻点系列 - 使用线条连接卡钻点
            series.push({
              name: curve.name + ' (卡钻)',
              type: 'line', // 使用线条类型以连接卡钻点
              xAxisIndex: gridIndex,
              yAxisIndex: gridIndex,
              data: stuckPointsData,
              showSymbol: true,
              symbol: 'circle',
              symbolSize: 12, // 增大符号尺寸
              connectNulls: false, // 不连接null值，只连接卡钻点
              lineStyle: {
                color: '#FF4D4F', // 红色连接线
                width: 3, // 线条宽度
                type: 'solid'
              },
              itemStyle: {
                color: '#FF4D4F', // 红色标记卡钻点
                borderColor: '#FFFFFF',
                borderWidth: 3,
                shadowBlur: 5,
                shadowColor: '#FF4D4F',
                shadowOffsetX: 0,
                shadowOffsetY: 0
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 15,
                  shadowColor: '#FF4D4F',
                  borderWidth: 4
                }
              },
              tooltip: {
                formatter: function(params) {
                  return `${curve.name}<br/>卡钻点: ${params.value}<br/>索引: ${params.dataIndex}`
                }
              },
              // 添加红色面积填充 - 增加颜色浓度
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#FF4D4F80' // 红色，80%透明度（更浓）
                  }, {
                    offset: 1,
                    color: '#FF4D4F30' // 红色，30%透明度（更浓）
                  }]
                }
              },
              // 添加 markPoint 标记点
              markPoint: {
                data: markPointData,
                symbol: 'pin', // 使用图钉样式
                symbolSize: [40, 50], // 标记点大小 [宽, 高]
                itemStyle: {
                  color: '#FF4D4F',
                  borderColor: '#FFFFFF',
                  borderWidth: 2,
                  shadowBlur: 8,
                  shadowColor: 'rgba(255, 77, 79, 0.5)',
                  shadowOffsetY: 2
                },
                label: {
                  show: true,
                  position: 'inside',
                  formatter: '卡',
                  color: '#FFFFFF',
                  fontWeight: 'bold',
                  fontSize: 12
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 12,
                    shadowColor: 'rgba(255, 77, 79, 0.8)'
                  },
                  label: {
                    fontSize: 12
                  }
                }
              },
              z: 10, // 设置较高的层级，确保在其他系列之上
              silent: false // 确保可以交互
            });
          }

          // 为原始数据曲线添加向上突变点标记 - 根据开关状态决定是否显示
          if (!curve.isFiltered && showMutationData.value && upMutationPointIndices && upMutationPointIndices.size > 0) {
            // 创建向上突变点数据 - 只在向上突变点位置显示数据
            const upMutationPointsData = curve.data.map((value, index) => {
              return upMutationPointIndices.has(index) ? value : null
            })

            // 创建向上突变点的 markPoint 数据 - 只在第一个网格（最上面的图表）的每段突变起始点添加
            const upMutationMarkPointData = []
            if (gridIndex === 0 && upMutationPointIndices.size > 0) { // 只在第一个网格添加 markPoint
              // 将向上突变点索引排序
              const sortedUpMutationIndices = Array.from(upMutationPointIndices).sort((a, b) => a - b)

              // 找到每段连续向上突变的起始点
              const upMutationSegmentStartPoints = [sortedUpMutationIndices[0]] // 第一个点肯定是起始点

              for (let i = 1; i < sortedUpMutationIndices.length; i++) {
                // 如果当前索引与前一个索引不连续，说明是新段的开始
                if (sortedUpMutationIndices[i] - sortedUpMutationIndices[i-1] > 1) {
                  upMutationSegmentStartPoints.push(sortedUpMutationIndices[i])
                }
              }

              // 为每个起始点创建 markPoint
              upMutationSegmentStartPoints.forEach(index => {
                const yValue = curve.data[index]
                if (yValue !== null && yValue !== undefined) {
                  upMutationMarkPointData.push({
                    xAxis: index, // 使用数据索引作为x坐标
                    yAxis: yValue, // 使用实际数值作为y坐标
                    name: '向上突变开始',
                    value: yValue
                  })
                }
              })
            }

            // 添加向上突变点系列 - 使用线条连接向上突变点
            series.push({
              name: curve.name + ' (向上突变)',
              type: 'line', // 使用线条类型以连接突变点
              xAxisIndex: gridIndex,
              yAxisIndex: gridIndex,
              data: upMutationPointsData,
              showSymbol: true,
              symbol: 'circle', // 使用圆形符号，与卡钻点保持一致
              symbolSize: 12, // 符号尺寸，与卡钻点保持一致
              connectNulls: false, // 不连接null值，只连接突变点
              lineStyle: {
                color: '#52C41A', // 绿色连接线，表示向上突变
                width: 2, // 线条宽度
                type: 'solid'
              },
              itemStyle: {
                color: '#52C41A', // 绿色标记向上突变点
                borderColor: '#FFFFFF',
                borderWidth: 2,
                shadowBlur: 3,
                shadowColor: '#52C41A',
                shadowOffsetX: 0,
                shadowOffsetY: 0
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: '#52C41A',
                  borderWidth: 3
                }
              },
              tooltip: {
                formatter: function(params) {
                  return `${curve.name}<br/>向上突变点: ${params.value}<br/>索引: ${params.dataIndex}`
                }
              },
              // 添加绿色面积填充
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#52C41A80' // 绿色，80%透明度
                  }, {
                    offset: 1,
                    color: '#52C41A30' // 绿色，30%透明度
                  }]
                }
              },
              // 添加 markPoint 标记点
              markPoint: {
                data: upMutationMarkPointData,
                symbol: 'pin', // 使用图钉样式，与卡钻点保持一致
                symbolSize: [40, 50], // 标记点大小 [宽, 高]，与卡钻点保持一致
                itemStyle: {
                  color: '#52C41A',
                  borderColor: '#FFFFFF',
                  borderWidth: 2,
                  shadowBlur: 8,
                  shadowColor: 'rgba(82, 196, 26, 0.5)',
                  shadowOffsetY: 2
                },
                label: {
                  show: true,
                  position: 'inside',
                  formatter: '↑',
                  color: '#FFFFFF',
                  fontWeight: '900',
                  fontSize: 20
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 12,
                    shadowColor: 'rgba(82, 196, 26, 0.8)'
                  },
                  label: {
                    fontSize: 22
                  }
                }
              },
              z: 9, // 设置层级，低于卡钻点但高于普通曲线
              silent: false // 确保可以交互
            });
          }

          // 为原始数据曲线添加向下突变点标记 - 根据开关状态决定是否显示
          if (!curve.isFiltered && showMutationData.value && downMutationPointIndices && downMutationPointIndices.size > 0) {
            // 创建向下突变点数据 - 只在向下突变点位置显示数据
            const downMutationPointsData = curve.data.map((value, index) => {
              return downMutationPointIndices.has(index) ? value : null
            })

            // 创建向下突变点的 markPoint 数据 - 只在第一个网格（最上面的图表）的每段突变起始点添加
            const downMutationMarkPointData = []
            if (gridIndex === 0 && downMutationPointIndices.size > 0) { // 只在第一个网格添加 markPoint
              // 将向下突变点索引排序
              const sortedDownMutationIndices = Array.from(downMutationPointIndices).sort((a, b) => a - b)

              // 找到每段连续向下突变的起始点
              const downMutationSegmentStartPoints = [sortedDownMutationIndices[0]] // 第一个点肯定是起始点

              for (let i = 1; i < sortedDownMutationIndices.length; i++) {
                // 如果当前索引与前一个索引不连续，说明是新段的开始
                if (sortedDownMutationIndices[i] - sortedDownMutationIndices[i-1] > 1) {
                  downMutationSegmentStartPoints.push(sortedDownMutationIndices[i])
                }
              }

              // 为每个起始点创建 markPoint
              downMutationSegmentStartPoints.forEach(index => {
                const yValue = curve.data[index]
                if (yValue !== null && yValue !== undefined) {
                  downMutationMarkPointData.push({
                    xAxis: index, // 使用数据索引作为x坐标
                    yAxis: yValue, // 使用实际数值作为y坐标
                    name: '向下突变开始',
                    value: yValue
                  })
                }
              })
            }

            // 添加向下突变点系列 - 使用线条连接向下突变点
            series.push({
              name: curve.name + ' (向下突变)',
              type: 'line', // 使用线条类型以连接突变点
              xAxisIndex: gridIndex,
              yAxisIndex: gridIndex,
              data: downMutationPointsData,
              showSymbol: true,
              symbol: 'circle', // 使用圆形符号，与卡钻点保持一致
              symbolSize: 12, // 符号尺寸，与卡钻点保持一致
              connectNulls: false, // 不连接null值，只连接突变点
              lineStyle: {
                color: '#FF6B35', // 橙红色连接线，表示向下突变
                width: 2, // 线条宽度
                type: 'solid'
              },
              itemStyle: {
                color: '#FF6B35', // 橙红色标记向下突变点
                borderColor: '#FFFFFF',
                borderWidth: 2,
                shadowBlur: 3,
                shadowColor: '#FF6B35',
                shadowOffsetX: 0,
                shadowOffsetY: 0
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: '#FF6B35',
                  borderWidth: 3
                }
              },
              tooltip: {
                formatter: function(params) {
                  return `${curve.name}<br/>向下突变点: ${params.value}<br/>索引: ${params.dataIndex}`
                }
              },
              // 添加橙红色面积填充
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: '#FF6B3580' // 橙红色，80%透明度
                  }, {
                    offset: 1,
                    color: '#FF6B3530' // 橙红色，30%透明度
                  }]
                }
              },
              // 添加 markPoint 标记点
              markPoint: {
                data: downMutationMarkPointData,
                symbol: 'pin', // 使用图钉样式，与卡钻点保持一致
                symbolSize: [40, 50], // 标记点大小 [宽, 高]，与卡钻点保持一致
                itemStyle: {
                  color: '#FF6B35',
                  borderColor: '#FFFFFF',
                  borderWidth: 2,
                  shadowBlur: 8,
                  shadowColor: 'rgba(255, 107, 53, 0.5)',
                  shadowOffsetY: 2
                },
                label: {
                  show: true,
                  position: 'inside',
                  formatter: '↓',
                  color: '#FFFFFF',
                  fontWeight: '900',
                  fontSize: 20
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 12,
                    shadowColor: 'rgba(255, 107, 53, 0.8)'
                  },
                  label: {
                    fontSize: 22
                  }
                }
              },
              z: 8, // 设置层级，低于向上突变点
              silent: false // 确保可以交互
            });
          }
        });
      }

      // 数据缩放控制器配置
      const dataZoom = [
        {
          type: 'slider',
          xAxisIndex: Array.from({length: gridCount}, (_, idx) => idx), // 应用到所有X轴
          start: 0,
          end: 100,
          filterMode: 'filter',
          handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6V24.4z M13.3,22H6.7v-1.2h6.6V22z M13.3,19.6H6.7v-1.2h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          }
        },
        {
          type: 'inside',
          xAxisIndex: Array.from({length: gridCount}, (_, idx) => idx), // 应用到所有X轴
          start: 0,
          end: 100,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true
        }
      ];

      // 返回完整的图表配置
      return {
        title: {
          text: xAxisType.value === 'dpth' ? '钻进深度曲线' : '钻进时间曲线',
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            animation: false
          },
          triggerOn: 'mousemove|click',  // 增加触发方式
          confine: true,  // 限制在图表区域内
          enterable: true,  // 允许鼠标进入tooltip
          extraCssText: 'max-width: 250px;',  // 设置最大宽度
          // 固定tooltip位置到右上角 - 使用像素坐标
          position: ['86%', 0],
          formatter: function(params) {
            // 确保params是数组
            if (!Array.isArray(params)) {
              params = [params];
            }

            // 只显示X轴数据，不显示曲线数据（因为灰色区域已经显示了各自的数据）
            const xValue = params[0]?.axisValue;
            if (xAxisType.value === 'dpth') {
              return `钻进深度: ${xValue} cm`;
            } else {
              // 格式化时间显示
              const date = new Date(xValue);
              const timeStr = date.getHours().toString().padStart(2, '0') + ':' +
                            date.getMinutes().toString().padStart(2, '0') + ':' +
                            date.getSeconds().toString().padStart(2, '0');
              return `采集时间: ${timeStr}`;
            }
          }
        },
        toolbox: {
          feature: {
            dataView: { readOnly: false },
            saveAsImage: {}
          }
        },
        axisPointer: {
          link: {xAxisIndex: 'all'}
        },
        legend: {
          data: curves.map(curve => curve.name),
          top: 40,
          left: 'center',
          textStyle: {
            fontSize: 12
          }
        },
        grid: grids,
        xAxis: xAxes,
        yAxis: yAxes,
        dataZoom: dataZoom,
        series: series
      };
    }

    // 创建防抖的图表更新函数
    const debouncedInitChart = debouncedChartUpdate(() => {
      nextTick(initChart)
    })

    // 合并所有数据源为一个计算属性，减少监听器数量
    // 包含开关状态，确保开关变化时图表能正确更新
    const chartDataSources = computed(() => ({
      drilling: props.drillingData,
      filtering: props.filteringData,
      // 只保留完整对象，在使用时再提取数组
      stuckData: props.stuckData,
      mutationData: props.mutationData,
      // 添加滤波数据的监听
      filterStuckData: props.filterStuckData,
      filterMutationData: props.filterMutationData,
      xAxisType: xAxisType.value,
      dataMode: dataDisplayMode.value,
      // 包含开关状态，确保开关变化时能触发图表更新
      showStuckData: showStuckData.value,
      showMutationData: showMutationData.value
    }))

    // 检查卡钻数据是否为空并自动关闭开关
    const checkStuckDataAndToggle = (stuckData) => {
      if (showStuckData.value) {
        // stuckData 是一个数组（stuckPoints），不是包含 stuckPoints 的对象
        const hasData = stuckData && Array.isArray(stuckData) && stuckData.length > 0

        if (!hasData) {
          showStuckData.value = false
          // 确保操作状态也被重置
          isOperating.value = false
          // 不在这里显示消息，因为父组件已经显示了
          return false
        } else {
          // 确保操作状态被重置
          isOperating.value = false
          // 不在这里显示消息，因为父组件已经显示了
          return true
        }
      }
      return true
    }

    // 检查突进数据是否为空并自动关闭开关
    const checkMutationDataAndToggle = (upMutationData, downMutationData) => {
      if (showMutationData.value) {
        // 检查数据是否为空或无效
        const hasUpData = upMutationData && upMutationData.length > 0
        const hasDownData = downMutationData && downMutationData.length > 0

        if (!hasUpData && !hasDownData) {
          showMutationData.value = false
          // 确保操作状态也被重置
          isOperating.value = false
          // 不在这里显示消息，因为父组件已经显示了
          return false
        } else {
          // 确保操作状态被重置
          isOperating.value = false
          // 不在这里显示消息，因为父组件已经显示了
          return true
        }
      }
      return true
    }

    // 使用单一监听器和防抖，优化性能
    watch(chartDataSources, (newData, oldData) => {
      // 简单的浅层比较，避免深度比较的性能开销
      if (JSON.stringify(newData) === JSON.stringify(oldData)) {
        return
      }

      // 检查各种数据变化
      const stuckDataChanged = newData.stuckData !== oldData?.stuckData ||
        JSON.stringify(newData.stuckData) !== JSON.stringify(oldData?.stuckData)
      const mutationDataChanged = newData.mutationData !== oldData?.mutationData ||
        JSON.stringify(newData.mutationData) !== JSON.stringify(oldData?.mutationData)
      // 添加滤波数据变化检测
      const filterStuckDataChanged = newData.filterStuckData !== oldData?.filterStuckData ||
        JSON.stringify(newData.filterStuckData) !== JSON.stringify(oldData?.filterStuckData)
      const filterMutationDataChanged = newData.filterMutationData !== oldData?.filterMutationData ||
        JSON.stringify(newData.filterMutationData) !== JSON.stringify(oldData?.filterMutationData)
      const basicDataChanged = newData.drilling !== oldData?.drilling ||
        newData.filtering !== oldData?.filtering ||
        newData.xAxisType !== oldData?.xAxisType ||
        newData.dataMode !== oldData?.dataMode



      // 处理卡钻数据变化（原始数据或滤波数据）
      if (stuckDataChanged || filterStuckDataChanged) {
        // 根据数据显示模式选择正确的数据源进行检查
        let stuckPoints = []
        if (newData.dataMode === 'original') {
          stuckPoints = newData.stuckData?.stuckPoints || []
        } else if (newData.dataMode === 'filtered') {
          stuckPoints = newData.filterStuckData?.stuckPoints || []
        } else if (newData.dataMode === 'both') {
          stuckPoints = newData.filterStuckData?.stuckPoints || newData.stuckData?.stuckPoints || []
        }
        checkStuckDataAndToggle(stuckPoints)
      }

      // 处理突进数据变化（原始数据或滤波数据）
      if (mutationDataChanged || filterMutationDataChanged) {
        // 根据数据显示模式选择正确的数据源进行检查
        let upMutationPoints = []
        let downMutationPoints = []
        if (newData.dataMode === 'original') {
          upMutationPoints = newData.mutationData?.upwardPoints || []
          downMutationPoints = newData.mutationData?.downwardPoints || []
        } else if (newData.dataMode === 'filtered') {
          upMutationPoints = newData.filterMutationData?.upwardPoints || []
          downMutationPoints = newData.filterMutationData?.downwardPoints || []
        } else if (newData.dataMode === 'both') {
          upMutationPoints = newData.filterMutationData?.upwardPoints || newData.mutationData?.upwardPoints || []
          downMutationPoints = newData.filterMutationData?.downwardPoints || newData.mutationData?.downwardPoints || []
        }
        checkMutationDataAndToggle(upMutationPoints, downMutationPoints)
      }

      // 更新本地数据副本（仅当钻井数据变化时）
      if (newData.drilling !== oldData?.drilling && newData.drilling && newData.drilling.length > 0) {
        try {
          localDrillingData.value = JSON.parse(JSON.stringify(newData.drilling))
        } catch (error) {
          console.error('数据深拷贝失败:', error);
          localDrillingData.value = [...newData.drilling]; // 降级为浅拷贝
        }
      } else if (!newData.drilling || newData.drilling.length === 0) {
        // 当接口返回空数据时，清空本地数据，避免显示旧数据
        localDrillingData.value = []
      }

      // 优化图表更新策略：
      // 1. 基础数据变化时：重新初始化图表
      // 2. 开启开关且有数据时：使用增量添加，不重绘图表
      // 3. 关闭开关时：在开关处理函数中已经重绘图表，这里不需要处理
      if (basicDataChanged) {
        // 基础数据变化时重新初始化图表
        debouncedInitChart()
      } else if ((stuckDataChanged || filterStuckDataChanged) && newData.showStuckData) {
        // 卡钻开关开启且数据变化（原始或滤波数据），使用优化的增量添加方法
        nextTick(() => {
          const result = addStuckDataSeries()
          if (result === false) {
            // 如果优化添加失败，回退到全量更新
            debouncedInitChart()
          }
        })
      } else if ((mutationDataChanged || filterMutationDataChanged) && newData.showMutationData) {
        // 突进开关开启且数据变化（原始或滤波数据），使用优化的增量添加方法
        nextTick(() => {
          const result = addMutationDataSeries()
          if (result === false) {
            // 如果优化添加失败，回退到全量更新
            debouncedInitChart()
          }
        })
      }
      // 注意：开关关闭时的图表重绘已在 handleStuckDataToggle 和 handleMutationDataToggle 中处理
    }, {
      deep: false, // 使用浅层监听，提高性能
      immediate: false // 不立即执行
    })

    // 注意：开关状态现在已经包含在 chartDataSources 中，会通过统一的数据监听器处理
    // 不再需要单独的开关状态监听器，避免重复触发

    // 组件挂载时初始化图表
    onMounted(() => {
      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      setTimeout(() => {
        initChart()

        // 如果初始化后高度仍然很小，再次尝试
        setTimeout(() => {
          if (chartInstance && lastCalculatedHeight < 300) {
            const newHeight = calculateChartHeight()
            if (newHeight !== lastCalculatedHeight && drillCurveChart.value) {
              lastCalculatedHeight = newHeight
              drillCurveChart.value.style.height = newHeight + 'px'
              initChart() // 重新初始化以应用正确的网格配置
            }
          }
        }, 300)
      }, 200)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        setTimeout(() => {
          if (drillCurveChart.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听图表本身导致无限循环
            const cardElement = drillCurveChart.value.parentElement?.parentElement // el-card
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理所有防抖函数
      if (debouncedInitChart && typeof debouncedInitChart.cancel === 'function') {
        debouncedInitChart.cancel()
      }

      if (handleResize && typeof handleResize.cancel === 'function') {
        handleResize.cancel()
      }

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
    })

    // 使用统一的防抖resize处理函数
    const handleResize = debouncedResize(() => {
      if (chartInstance && drillCurveChart.value) {
        // 重新计算图表高度以适应容器变化
        const newHeight = calculateChartHeight()

        // 只有当高度变化超过10px时才更新，避免无限循环
        if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
          lastCalculatedHeight = newHeight
          drillCurveChart.value.style.height = newHeight + 'px'

          // 重新初始化图表以应用新的网格配置
          setTimeout(() => {
            if (chartInstance) {
              initChart() // 重新初始化以重新计算网格布局
            }
          }, 100)
        }
      }
    })

    return {
      drillCurveChart,
      xAxisType,
      xAxisOptions,
      dataDisplayMode,
      depthStep,
      showStuckData,
      showMutationData,
      isOperating,
      handleAxisTypeChange,
      handleDataSourceChange,
      handleDepthStepChange,
      handleStuckDataToggle,
      handleMutationDataToggle,
      editDialogVisible,
      editForm,
      getCurveUnit,
      getCurveTagType,
      handleDialogClose,
      handleSaveEdit,
      updateSingleDataPoint
    }
  }
}
</script>

<style scoped>
.drill-curve-chart-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.drill-curve-chart-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.drill-curve-chart-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.drill-curve-chart {
  width: 100%;
  /* 移除固定高度，让图表能够自适应外部容器 */
  /* 高度由JavaScript动态计算和设置 */
}

.chart-controls {
  display: flex;
  align-items: center;
}

.data-source-switch {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.data-source-switch :deep(.el-radio-group) {
  border-radius: 4px;
}

.data-source-switch :deep(.el-radio-button__inner) {
  padding: 8px 12px;
  font-size: 12px;
  border-radius: 0;
}

.data-toggle-switches {
  display: flex;
  align-items: center;
  margin-right: 20px;
  gap: 15px;
}

.toggle-switch-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.switch-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.data-source-switch :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.data-source-switch :deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.depth-step-select {
  margin-right: 20px;
}

.axis-tabs {
  margin-left: 20px;
}

/* 自定义tab样式 */
.axis-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.axis-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.axis-tabs :deep(.el-tabs__item) {
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.axis-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

.axis-tabs :deep(.el-tabs__active-bar) {
  height: 2px;
  background-color: #409eff;
}

/* 编辑对话框样式 */
.unit-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 