<template>
  <div class="import-detail-container">
    <div class="header-container">
      <el-page-header @back="goBack">
        <template #content>
          <span class="file-name">{{ fileName }}</span>
          <span class="page-title text-primary">导入详情</span>
        </template>
      </el-page-header>
    </div>

    <el-card
      v-loading="loading"
      class="import-detail-card"
    >
      <template #header>
        <div class="card-header">
          <span>文件信息</span>
        </div>
      </template>

      <el-descriptions
        :column="2"
        border
      >
        <el-descriptions-item label="文件名称">
          {{ importDetail.fileName }}
        </el-descriptions-item>
        <el-descriptions-item label="导入时间">
          {{ importDetail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="导入设备数">
          {{ importDetail.importRows }}
        </el-descriptions-item>
        <el-descriptions-item label="OSS路径">
          {{ importDetail.ossPath }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 文件内容展示 -->
      <div class="content-section">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="content">
            <template #title>
              <div class="content-header">
                <h3>文件内容</h3>
                <span
                  v-if="!activeNames.includes('content')"
                  class="click-hint"
                >点击展开</span>
                <span
                  v-else
                  class="click-hint"
                >点击收起</span>
              </div>
            </template>
            <div class="content-box">
              <pre v-if="importDetail.content">{{ importDetail.content }}</pre>
              <el-empty
                v-else
                description="无内容"
              />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 设备列表 -->
    <el-card
      v-loading="devicesLoading"
      class="device-list-card"
    >
      <template #header>
        <div class="card-header">
          <span>关联设备列表</span>
        </div>
      </template>

      <el-table
        :data="deviceList"
        stripe
        style="width: 100%"
        max-height="546"
      >
        <!-- 新增的序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"
        />
        <el-table-column
          prop="type"
          label="产品类型"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="getDeviceTagType(scope.row.type)"
              effect="light"
            >
              {{ scope.row.typeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          align="center"
        />
        <el-table-column
          prop="deviceSn"
          label="设备序列号"
          align="center"
        />
        <el-table-column
          prop="deviceMac"
          label="MAC地址"
          align="center"
        />
        <el-table-column
          label="创建时间"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.createdAt }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :total="deviceTotal"
          :current-page="devicePage"
          :page-size="devicePageSize"
          :page-sizes="[10, 20, 40, 50, 60, 80, 100]"
          @current-change="handleDevicePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 导入详情页面
 * 显示导入文件的详细信息和导入的设备列表
 */

import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getImportDetail, getImportDevices } from '@/api'
import { getDeviceTagType } from '@/utils/utils'

// 导入详情类型
interface ImportDetail {
  id: string
  fileName: string
  ossPath: string
  content: string
  importRows: number
  createdAt: string
}

// 设备信息类型
interface Device {
  id: string
  device_name: string
  device_sn: string
  product_key: string
  type?: string
  createdAt?: string
}

// API响应类型
interface ImportDetailResponse {
  success: boolean
  data: ImportDetail
}

interface DeviceListResponse {
  success: boolean
  data: {
    list: Device[]
    total: number
  }
}

const route = useRoute()
const router = useRouter()
const importId = route.params.id as string
const fileName = ref<string>(route.query.fileName as string || '导入详情')
const loading = ref<boolean>(false)
const devicesLoading = ref<boolean>(false)

// 导入详情数据
const importDetail = ref<ImportDetail>({
  id: '',
  fileName: '',
  ossPath: '',
  content: '',
  importRows: 0,
  createdAt: ''
})

// 控制折叠面板的展开状态，默认为空数组表示全部折叠
const activeNames = ref<string[]>([])

// 设备列表相关
const deviceList = ref<Device[]>([])
const deviceTotal = ref<number>(0)
const devicePage = ref<number>(1)
const devicePageSize = ref<number>(10)

// 获取导入详情数据
const fetchImportDetail = async () => {
  if (!importId) {
    ElMessage.error('未找到导入ID')
    return
  }

  loading.value = true
  try {
    const response = await getImportDetail(importId)

    if (response.success) {
      importDetail.value = response.data
      // 如果路由没有传递fileName，使用接口返回的
      if (!route.query.fileName) {
        fileName.value = response.data.fileName || '导入详情'
      }
    } else {
      ElMessage.error('获取导入详情失败')
    }
  } catch (error) {
    console.error('获取导入详情出错:', error)
    ElMessage.error('获取导入详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取导入关联的设备列表
const fetchImportDevices = async () => {
  if (!importId) {
    return
  }

  devicesLoading.value = true
  try {
    const response = await getImportDevices(importId, {
      page: devicePage.value,
      pageSize: devicePageSize.value
    })

    if (response.success) {
      deviceList.value = response.data.list
      deviceTotal.value = response.data.total
    } else {
      ElMessage.error('获取设备列表失败')
    }
  } catch (error) {
    console.error('获取设备列表出错:', error)
    ElMessage.error('获取设备列表失败: ' + (error.message || '未知错误'))
  } finally {
    devicesLoading.value = false
  }
}

// 设备列表页码变化
const handleDevicePageChange = page => {
  devicePage.value = page
  fetchImportDevices()
}

// 页面大小变化
const handleSizeChange = val => {
  devicePageSize.value = val
  devicePage.value = 1 // 重置到第一页
  fetchImportDevices()
}


// 返回上一页
const goBack = () => {
  router.push('/import')
}

onMounted(() => {
  // 加载导入详情数据
  fetchImportDetail()
  // 加载设备列表
  fetchImportDevices()
})
</script>

<style scoped>
.import-detail-container {
  padding: 24px;
  background-color: var(--apple-background);
}

.header-container {
  margin-bottom: 20px;
}

.file-name {
  font-size: 18px;
  font-weight: bold;
  margin-right: 10px;
}

.page-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

.import-detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28px;
  font-weight: 500;
}

.content-section {
  margin-top: 20px;
}

.content-section :deep(.el-collapse-item__header) {
  border: none;
  padding: 0;
}

.content-section :deep(.el-collapse-item__header) h3 {
  margin: 0;
  font-size: 16px;
}

.content-section :deep(.el-collapse-item__wrap) {
  border: none;
}

.content-section :deep(.el-collapse-item__content) {
  padding: 0;
}

.content-section :deep(.el-collapse) {
  border: none;
}

.content-header {
  display: flex;
  align-items: center;
}

.click-hint {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.content-box {
  margin-top: 10px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
}

:deep(.el-card__header) {
  border-bottom: none;
}

/* 设备列表卡片样式 */
.device-list-card {
  margin-bottom: 20px;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style>
