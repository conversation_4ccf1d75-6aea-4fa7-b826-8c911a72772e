<template>
  <div class="import-container">
    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card class="import-list-card">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-input
              v-model.trim="searchForm.fileName"
              placeholder="请输入文件名称"
              clearable
              style="width: 100%"
              @keyup.enter="debouncedHandleSearch"
            />
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-col>
          <el-col
            :span="4"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedHandleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
          <el-col
            :span="8"
            style="text-align: right"
          >
            <el-button
              type="primary"
              @click="dialogVisible = true"
            >
              生产导入
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 导入列表 -->
      <el-table
        v-loading="loading"
        :data="importList"
        style="width: 100%"
        max-height="546"
        stripe
      >
        <!-- 新增的序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column
          prop="fileName"
          label="导入文件名称"
          align="center"
        />
        <el-table-column
          prop="importRows"
          label="导入设备数"
          width="150"
          align="center"
        />
        <el-table-column
          prop="failedRows"
          label="导入失败"
          width="150"
          align="center"
        />
        <el-table-column
          prop="skippedRows"
          label="跳过导入"
          width="150"
          align="center"
        />
        <el-table-column
          prop="createdAt"
          label="导入时间"
          width="250"
          align="center"
        />
        <el-table-column
          label="操作"
          width="150"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="viewImportDetail(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 40, 50, 60, 80, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 导入文件对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="生产导入"
      width="500px"
      class="import-dialog"
    >
      <el-form
        :model="importForm"
        label-width="0"
      >
        <el-form-item>
          <div class="upload-container">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              accept=".csv,text/csv"
              multiple
              :limit="10"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">
                拖拽文件到此处或 <em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  仅支持 .csv 格式文件，最多可选择10个文件
                  <el-button
                    type="primary"
                    link
                    @click="downloadTemplate"
                  >
                    下载模板
                  </el-button>
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitImport"
          >确认导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 导入页面
 * 提供文件导入功能和导入历史记录管理
 */

import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getImportList, createImport, batchCheckFileNames } from '@/api'
import { debouncedApiCall } from '@/utils/debounce'
import { useImportListStore } from '@/store/importList'
import { storeToRefs } from 'pinia'

// 搜索表单类型
interface SearchForm {
  fileName: string
  dateRange: string[]
}

// 文件项类型
interface FileItem {
  file: File
  content: string
  fileName: string
  rows: number
  [key: string]: any
}

// 导入表单类型
interface ImportForm {
  fileItems: FileItem[]
}

// 导入记录类型
interface ImportRecord {
  id: string
  fileName: string
  status: string
  createdAt: string
  totalRows?: number
  successRows?: number
  failedRows?: number
  [key: string]: any
}

// API响应类型
interface ImportListResponse {
  success: boolean
  data: {
    list: ImportRecord[]
    total: number
  }
}

const router = useRouter()

// 使用导入列表Store
const importListStore = useImportListStore()
const { filterForm, currentPage, pageSize, total } = storeToRefs(importListStore)

// 将Store的filterForm映射为searchForm以保持兼容性
const searchForm = filterForm

// 导入表单
const importForm = reactive<ImportForm>({
  fileItems: [] // 存储文件项，每项包含file、content、fileName、rows等信息
})

// 加载状态
const loading = ref<boolean>(false)
const submitting = ref<boolean>(false)

// 导入列表数据
const importList = ref<ImportRecord[]>([])

// 对话框控制
const dialogVisible = ref<boolean>(false)
// 上传组件引用
const uploadRef = ref<any>(null)

// 监听对话框关闭事件
watch(dialogVisible, newVal => {
  if (!newVal) {
    // 对话框关闭时重置表单和清空已上传文件
    resetImportForm()
  }
})

// 重置导入表单
const resetImportForm = () => {
  // 重置表单数据
  importForm.fileItems = []

  // 清空上传的文件列表
  if (uploadRef.value) {
    // 使用uploadRef的clearFiles方法清空文件列表
    uploadRef.value.clearFiles()

    // 确保uploadFiles数组也被清空
    if (uploadRef.value.uploadFiles) {
      uploadRef.value.uploadFiles.length = 0
    }
  }
}

// 获取导入列表数据
const fetchImportList = async () => {
  loading.value = true
  try {
    const params = {
      file_name: searchForm.value.fileName,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加时间段参数
    if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
      Object.assign(params, {
        start_date: searchForm.value.dateRange[0],
        end_date: searchForm.value.dateRange[1]
      })
    }

    const response = await getImportList(params)

    if (response.success) {
      importList.value = response.data.list
      total.value = response.data.total
    } else {
      ElMessage.error('获取导入列表失败')
    }
  } catch (error) {
    console.error('获取导入列表出错:', error)
    ElMessage.error('获取导入列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchImportList()
}

// 使用防抖的搜索函数
const debouncedHandleSearch = debouncedApiCall(() => {
  currentPage.value = 1
  fetchImportList()
})

// 重置表单
const resetForm = () => {
  importListStore.resetState()
  handleSearch()
}

// 检测文本是否包含乱码
const hasGarbledText = (text: string): boolean => {
  // 检查是否包含替换字符（乱码的典型标志）
  if (text.includes('�')) return true

  // 检查标题行中的中文列名是否正常
  const lines = text.split('\n')
  if (lines.length > 0) {
    const header = lines[0].toLowerCase()
    // 如果包含预期的中文列名，检查是否显示正常
    if (header.includes('设备') || header.includes('项目')) {
      // 检查中文字符是否在正常范围内
      const chinesePattern = /[\u4e00-\u9fa5]/g
      const chineseMatches = header.match(chinesePattern)
      if (chineseMatches && chineseMatches.length > 0) {
        // 如果有中文字符，检查是否有异常字符
        const abnormalPattern = /[^\x00-\x7F\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF,]/g
        return abnormalPattern.test(header)
      }
    }
  }

  return false
}

// 使用指定编码读取文件
const readFileWithEncoding = (file: File, encoding = 'utf-8'): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer
        if (!arrayBuffer) {
          reject(new Error('文件读取失败'))
          return
        }
        const decoder = new TextDecoder(encoding)
        const content = decoder.decode(arrayBuffer)
        resolve(content)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取出错'))
    reader.readAsArrayBuffer(file)
  })
}

// 尝试多种编码读取文件
const readFileWithAutoEncoding = async (file: File): Promise<string> => {
  // 常见的中文编码格式，按优先级排序
  const encodings = ['utf-8', 'gbk', 'gb2312', 'big5']

  for (const encoding of encodings) {
    try {
      const content = await readFileWithEncoding(file, encoding)

      // 检查是否包含乱码
      if (!hasGarbledText(content)) {
        console.log(`文件 ${file.name} 使用编码: ${encoding}`)
        return content
      }
    } catch (error) {
      console.warn(`尝试编码 ${encoding} 失败:`, error)
      continue
    }
  }

  // 如果所有编码都失败，返回UTF-8结果并给出警告
  console.warn(`文件 ${file.name} 无法确定正确编码，使用UTF-8`)
  return await readFileWithEncoding(file, 'utf-8')
}

// 文件变更处理
const handleFileChange = async (file: any, _fileList: any) => {
  // 对于新添加的文件进行处理
  if (file.status === 'ready') {
    const fileName = file.name || ''
    const fileExtension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()

    // 检查文件类型
    if (fileExtension !== '.csv') {
      ElMessage.warning(`文件 ${fileName} 不是.csv格式，已过滤`)
      // 从上传列表中移除非CSV文件
      if (uploadRef.value) {
        uploadRef.value.handleRemove(file)
      }
      return
    }

    // 添加到待处理文件项
    const newItem = {
      file: file.raw,
      fileName: file.name,
      content: '',
      processed: false,
      valid: true // 添加有效性标记
    }

    try {
      // 使用自动编码检测读取文件内容
      const content = await readFileWithAutoEncoding(file.raw)
      newItem.content = content.trim()

      // 验证CSV文件是否包含必要的列
      const validationResult = validateCsvColumns(newItem.content, newItem.fileName)
      if (!validationResult.isValid) {
        // 标记为无效
        newItem.valid = false
        ElMessage.error(validationResult.message)

        // 从上传组件中移除该文件
        if (uploadRef.value) {
          setTimeout(() => {
            // 使用setTimeout确保DOM更新后再移除文件
            uploadRef.value.handleRemove(file)
          }, 100)
        }
      } else {
        // 标记为已处理
        newItem.processed = true
        // 添加到文件项数组
        importForm.fileItems.push(newItem)
      }
    } catch (error: any) {
      console.error(`读取文件 ${fileName} 失败:`, error)
      ElMessage.error(`读取文件 ${fileName} 失败: ${error?.message || '未知错误'}`)

      // 从上传组件中移除该文件
      if (uploadRef.value) {
        setTimeout(() => {
          uploadRef.value.handleRemove(file)
        }, 100)
      }
    }
  }
}

// 处理文件删除
const handleFileRemove = (file: any) => {
  console.log('删除文件:', file.name)

  // 从 importForm.fileItems 中移除对应的文件项
  const index = importForm.fileItems.findIndex((item: any) => item.fileName === file.name)
  if (index !== -1) {
    importForm.fileItems.splice(index, 1)
    console.log(`已从导入列表中移除文件: ${file.name}`)
  }
}

// 验证CSV是否包含必要的列
const validateCsvColumns = (content: string, fileName: string): { isValid: boolean; message?: string } => {
  // 确保内容不为空
  if (!content || content.trim() === '') {
    return {
      isValid: false,
      message: `文件 ${fileName} 内容为空`
    }
  }

  // 将内容按行分割
  const lines = content.split('\n').filter(line => line.trim() !== '')

  // 确保至少有标题行
  if (lines.length === 0) {
    return {
      isValid: false,
      message: `文件 ${fileName} 内容为空`
    }
  }

  // 检查标题行是否包含必要的列
  const header = lines[0].toLowerCase()
  const requiredColumns = ['productkey', 'sn', 'secret', 'mac', '设备名称', '项目名称']
  const missingColumns: string[] = []

  for (const column of requiredColumns) {
    if (!header.includes(column)) {
      missingColumns.push(column)
    }
  }

  if (missingColumns.length > 0) {
    return {
      isValid: false,
      message: `文件 ${fileName} 缺少必要的列: ${missingColumns.join(', ')}，请重新上传`
    }
  }

  return { isValid: true }
}

// 提交导入
const submitImport = async () => {
  // 过滤只保留有效且处理完成的文件
  const validItems = importForm.fileItems.filter((item: any) => item.valid && item.processed)

  if (validItems.length === 0) {
    ElMessage.warning('请选择要导入的有效文件')
    return
  }

  console.log('准备导入的文件:', validItems.map((item: any) => item.fileName))

  submitting.value = true
  let successCount = 0
  let failCount = 0

  try {
    // 批量检查文件名是否已存在
    const fileNames = validItems.map((item: any) => item.fileName)
    const checkResponse = await batchCheckFileNames(fileNames)

    if (!checkResponse.success) {
      ElMessage.error('检查文件名失败')
      return
    }

    // 获取可导入的文件名列表
    const { availableFileNames, availableCount, existingCount } = checkResponse.data

    if (availableCount === 0) {
      ElMessage.warning('没有可导入的文件')
      return
    }

    // 显示检查结果
    if (existingCount > 0) {
      ElMessage.warning(`${existingCount} 个文件已存在将跳过，${availableCount} 个文件可以导入`)
      failCount = existingCount
    }

    // 根据可导入的文件名过滤文件项
    const validItemsToImport = validItems.filter((item: any) => availableFileNames.includes(item.fileName))

    // 导入过滤后的文件
    for (const item of validItemsToImport) {
      try {
        const response = await createImport({
          file_name: item.fileName,
          content: item.content
        })

        if (response.success) {
          const createdDevices = response.data.createdDevices || 0
          successCount++
          console.log(`文件 ${item.fileName} 成功导入，创建了 ${createdDevices} 个设备`)
        } else {
          failCount++
          console.error(`文件 ${item.fileName} 导入失败: ${response.message || '未知错误'}`)
        }
      } catch (error) {
        failCount++
        console.error(`文件 ${item.fileName} 导入出错:`, error)
      }
    }

    // 显示导入结果
    if (successCount > 0 && failCount === 0) {
      ElMessage.success(`成功导入 ${successCount} 个文件，设备数据已添加到设备管理中`)
    } else if (successCount > 0 && failCount > 0) {
      ElMessage.warning(`成功导入 ${successCount} 个文件，${failCount} 个文件导入失败`)
    } else {
      ElMessage.error('所有文件导入失败')
    }

    // 关闭对话框
    if (successCount > 0) {
      dialogVisible.value = false

      // 刷新列表
      fetchImportList()
    }
  } catch (error) {
    console.error('导入文件出错:', error)
    ElMessage.error('导入文件失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 查看导入详情
const viewImportDetail = row => {
  // 跳转到详情页面，并传递ID参数和文件名
  router.push({
    path: `/import/detail/${row.id}`,
    query: { fileName: row.fileName }
  })
}

// 页码变化
const handleCurrentChange = val => {
  currentPage.value = val
  fetchImportList()
}

// 页面大小变化
const handleSizeChange = val => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  fetchImportList()
}

// 下载模板
const downloadTemplate = () => {
  // 创建CSV内容，包含所有验证时需要的列
  const templateContent = 'ProductKey,SN,Secret,Mac,设备名称,项目名称\n'

  // 创建Blob对象
  const blob = new Blob([templateContent], { type: 'text/csv;charset=utf-8;' })

  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  // 获取当前时间戳 (格式: yyyyMMddHHmmss)
  const now = new Date()
  const timestamp = [
    now.getFullYear(),
    (now.getMonth() + 1).toString().padStart(2, '0'),
    now.getDate().toString().padStart(2, '0'),
    now.getHours().toString().padStart(2, '0'),
    now.getMinutes().toString().padStart(2, '0'),
    now.getSeconds().toString().padStart(2, '0')
  ].join('')

  // 设置下载属性，文件名带时间戳
  link.setAttribute('href', url)
  link.setAttribute('download', `import_template_${timestamp}.csv`)
  link.style.visibility = 'hidden'

  // 添加到文档并触发点击
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('模板下载成功')
}

// 关闭对话框按钮
const handleCloseDialog = () => {
  dialogVisible.value = false
}

// 监听状态变化，自动保存状态
watch([() => searchForm.value.fileName, () => searchForm.value.dateRange,
       () => currentPage.value, () => pageSize.value],
  () => {
    importListStore.saveState()
  },
  { deep: true }
)

onMounted(() => {
  // 恢复保存的搜索状态
  const restored = importListStore.restoreState()
  if (restored) {
    console.log('已恢复导入列表搜索状态')
  }

  // 初始加载数据
  fetchImportList()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedHandleSearch && typeof debouncedHandleSearch.cancel === 'function') {
    debouncedHandleSearch.cancel()
  }
})
</script>

<style scoped>
.import-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 整合搜索和列表的卡片 */
.import-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

/* 按钮区域样式 */
.filter-buttons {
  display: flex;
  gap: 10px;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 导入弹框样式 */
.import-dialog :deep(.el-dialog__body) {
  text-align: center;
}

.upload-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.upload-container :deep(.el-upload) {
  width: 100%;
}

.upload-container :deep(.el-upload-dragger) {
  width: 100%;
}
</style>
