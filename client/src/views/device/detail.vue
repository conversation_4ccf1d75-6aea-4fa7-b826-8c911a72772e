<template>
  <div class="device-detail-container analysis-dashboard">
    <!-- 保留原有的返回头部 -->
    <el-page-header @back="goBack">
      <template #content>
        <div class="device-header-info">
          <span class="device-name">{{ deviceName }}</span>
          <span class="device-title text-primary">设备详情</span>
          <span class="device-status">
            <el-tag
              :type="deviceOnline ? 'success' : 'danger'"
              effect="dark"
              size="small"
              class="status-tag"
            >
              {{ deviceOnline ? '在线' : '离线' }}
            </el-tag>
            <span
              v-if="!deviceOnline && lastDataTime"
              class="offline-time"
            >
              最后活跃: {{ formatDateTime(lastDataTime) }}
            </span>
          </span>
        </div>
      </template>
      <template #extra>
        <el-button
          type="success"
          size="small"
          @click="goToRealtime"
        >
          <el-icon><Connection /></el-icon>
          实时数据
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="goToDigitalCoreView"
        >
          <el-icon><View /></el-icon>
          数字岩芯
        </el-button>
      </template>
    </el-page-header>

    <!-- 顶部信息栏 -->
    <div
      v-loading="loading"
      class="top-info-bar"
    >
      <div class="device-info-section">
        <div class="info-content">
          <div class="info-item-row">
            <div class="info-items">
              <div class="info-item">
                <div class="label">
                  {{ deviceDetail?.typeName || '无数据' }}
                </div>
                <div class="value">
                  设备类型
                </div>
              </div>
              <!-- <div class="info-item">
                <div class="label">
                  {{ deviceDetail?.deviceName || '无数据' }}
                </div>
                <div class="value">
                  设备名称
                </div>
              </div> -->
              <div class="info-item">
                <div class="label">
                  {{ lastDataTunnelName || deviceDetail?.projectName || '无数据' }}
                </div>
                <div class="value">
                  项目名称
                </div>
              </div>
              <div class="info-item">
                <div class="label">
                  {{ deviceDetail?.serialNumber || '无数据' }}
                </div>
                <div class="value">
                  设备编号
                </div>
              </div>
              <div class="info-item">
                <div class="label">
                  {{ deviceDetail?.macAddress || '无数据' }}
                </div>
                <div class="value">
                  MAC地址
                </div>
              </div>
              <div class="info-item">
                <div class="label">
                  {{ deviceDetail?.deviceSecret || '无数据' }}
                </div>
                <div class="value">
                  设备密钥
                </div>
              </div>
              <div class="info-item">
                <div class="label">
                  {{ deviceDetail?.modifiedAt || '无数据' }}
                </div>
                <div class="value">
                  最后修改时间
                </div>
              </div>
            </div>
            <div class="edit-btn">
              <el-button
                type="primary"
                size="small"
                circle
                @click="openEditDialog"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="algorithms-section">
        <div class="algo-content">
          <div
            class="algorithm-item"
            @click="openAlgorithmDialog(0)"
          >
            <div class="algo-info">
              <div class="algo-name">
                {{ cleanAlgorithm || '未设置' }}
              </div>
              <div class="algo-title">
                清洗算法
              </div>
            </div>
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
          <div
            class="algorithm-item"
            @click="openAlgorithmDialog(1)"
          >
            <div class="algo-info">
              <div class="algo-name">
                {{ analysisAlgorithm || '未设置' }}
              </div>
              <div class="algo-title">
                控件算法
              </div>
            </div>
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 分析报告区域 - 修改为包含所有子卡片的大卡片 -->
    <el-card class="report-section">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span>分析报告</span>
          </div>
          <div class="header-right">
            <el-select
              v-model="selectedFile"
              filterable
              placeholder="选择设备文件"
              style="width: 280px"
              :loading="fileLoading"
              :disabled="false"
              @change="handleFileChange"
            >
              <el-option
                v-for="item in deviceFiles"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :disabled="false"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 380px"
              @change="handleTimeRangeChange"
            />
            <el-input
              v-model="selectedHoleNo"
              placeholder="请输入孔号"
              clearable
              style="width: 180px"
              :disabled="false"
              @input="handleHoleNoChange"
            >
              <template #prepend>
                <div>
                  孔号
                </div>
              </template>
            </el-input>
            <el-input
              v-model="queryLimit"
              placeholder="数据条数"
              type="number"
              style="width: 180px"
              :min="1"
              :max="10000"
            >
              <template #prepend>
                <div>
                  数据条数
                </div>
              </template>
            </el-input>
            <el-button 
              type="primary" 
              style="margin-left: 10px"
              :disabled="!canSearch"
              @click="debouncedHandleSearch"
            >
              查询
            </el-button>
            
            <!-- 添加图表配置按钮 -->
            <el-button
              type="info"
              size="default"
              @click="goToChartConfig"
            >
              <el-icon><Setting /></el-icon>
              图表配置
            </el-button>

            <!-- 添加使用模版开关 -->
            <el-switch
              v-model="useTemplate"
              size="default"
              active-text="使用模版"
              style="margin-left: 12px;"
              @change="handleTemplateToggle"
            />
          </div>
        </div>
      </template>

      <!-- 根据开关状态显示不同组件 - 使用v-show避免组件挂载卸载导致的抖动 -->
      <AnalysisReportDisplay
        v-show="!useTemplate"
        :loading="loading"
        :report-data="reportData"
        :available-charts="availableCharts"
        @stuck-toggle="handleStuckToggle"
        @mutation-toggle="handleMutationToggle"
      />

      <!-- 设备模版展示组件 -->
      <DeviceTemplateDisplay
        v-show="useTemplate"
        :device-id="deviceId"
        :loading="templateLoading"
        :visible="useTemplate"
        :report-data="reportData"
        @stuck-toggle="handleStuckToggle"
        @mutation-toggle="handleMutationToggle"
      />
    </el-card>

    <!-- 算法选择对话框 -->
    <el-dialog
      v-model="algorithmDialogVisible"
      :title="currentAlgorithmType === 0 ? '选择清洗算法' : '选择控件算法'"
      width="50%"
    >
      <div
        v-loading="algorithmLoading"
        class="algorithm-dialog-content"
      >
        <div
          v-if="algorithmList.length === 0"
          class="no-algorithm"
        >
          暂无可用的{{ currentAlgorithmType === 0 ? '清洗' : '控件' }}算法
        </div>
        <el-table
          v-else
          ref="algorithmTableRef"
          :data="algorithmList"
          style="width: 100%"
          max-height="546"
          highlight-current-row
          :default-sort="{ prop: 'isActive', order: 'descending' }"
          @current-change="handleAlgorithmSelect"
        >
          <el-table-column
            prop="name"
            label="算法名称"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="usedDeviceCount"
            label="应用设备数"
            align="center"
          >
            <template #default="scope">
              <span>{{ scope.row.usedDeviceCount || scope.row.usedNum || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="状态&下载"
            width="150"
            align="center"
          >
            <template #default="scope">
              <div
                v-if="
                  (currentAlgorithmType === 0 && scope.row.id === currentCleanAlgorithmId) ||
                    (currentAlgorithmType === 1 && scope.row.id === currentAnalysisAlgorithmId)
                "
                style="display: flex; align-items: center; justify-content: center; gap: 8px"
              >
                <el-tag
                  type="primary"
                  effect="light"
                  size="small"
                >
                  应用中
                </el-tag>
                <el-button
                  type="primary"
                  size="small"
                  @click="downloadAlgorithm(scope.row)"
                >
                  下载
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="modifiedAt"
            label="更新时间"
            align="center"
          />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="algorithmDialogVisible = false">取消</el-button>
          <el-button
            :type="isSelectedAlgorithmApplied ? 'danger' : 'primary'"
            :disabled="!selectedAlgorithmId"
            :loading="algorithmLoading || cancelLoading"
            @click="handleAlgorithmAction"
          >
            {{ isSelectedAlgorithmApplied ? '取消应用' : '应用' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 设备信息编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑设备信息"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        label-width="100px"
        :rules="editRules"
      >
        <el-form-item
          label="设备名称"
          prop="deviceName"
        >
          <el-input
            v-model="editForm.deviceName"
            placeholder="请输入设备名称"
          />
        </el-form-item>
        <el-form-item
          label="项目名称"
          prop="projectName"
        >
          <el-input
            v-model="editForm.projectName"
            placeholder="请输入项目名称"
          />
        </el-form-item>
        <el-form-item
          label="设备编号"
          prop="serialNumber"
        >
          <el-input
            v-model="editForm.serialNumber"
            placeholder="设备编号"
            disabled
          />
          <div class="form-item-tip">
            设备编号不可修改
          </div>
        </el-form-item>
        <el-form-item
          label="MAC地址"
          prop="macAddress"
        >
          <el-input
            v-model="editForm.macAddress"
            placeholder="请输入MAC地址"
          />
        </el-form-item>
        <el-form-item
          label="设备密钥"
          prop="deviceSecret"
        >
          <el-input
            v-model="editForm.deviceSecret"
            placeholder="请输入设备密钥"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="editLoading"
            @click="submitEditForm"
          >保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 设备详情页面
 * 显示设备详细信息、算法配置、数据分析等功能
 */

import { ref, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDeviceDetail,
  getDeviceArithmetic,
  setDeviceArithmetic,
  deleteDeviceArithmetic,
  getArithmeticList,
  updateDevice
} from '@/api'
import { downloadArithmetic } from '@/api/arithmetic'
import { fileApi } from '@/api/file'
import { queryDrillingData, queryDataWithAlgorithms, getLatestDeviceData } from '@/api/data'
import { ArrowRight, Edit, Connection, View, Setting } from '@element-plus/icons-vue'

// 导入分析报告展示组件
import AnalysisReportDisplay from '@/components/device/AnalysisReportDisplay.vue'
// 导入设备模版展示组件
import DeviceTemplateDisplay from '@/components/device/DeviceTemplateDisplay.vue'

// 导入工具函数
import { formatDateTime } from '@/utils/utils'
import { debouncedApiCall } from '@/utils/debounce'

// 添加图表配置相关API
import { getDeviceEnabledCharts } from '@/api/chartConfig'

// 设备详情类型
interface DeviceDetail {
  id: string
  device_sn: string
  device_name: string
  product_key: string
  project_name?: string
  type?: string
  lastDataTime?: string
  lastFileTime?: string
  macAddress?: string
  createdAt?: string
  modifiedAt?: string
}

// 算法信息类型
interface Algorithm {
  id: string
  name: string
  description?: string
  version?: string
  status?: string
}

// 报告数据类型
interface ReportData {
  analysisReport?: any[]
  geologicAnalysisReport?: any[]
  [key: string]: any
}

const route = useRoute()
const router = useRouter()
const deviceId = route.params.id as string
const deviceName = ref<string>('')
const deviceDetail = ref<DeviceDetail | null>(null)
const loading = ref<boolean>(false)

// 添加数据结构响应相关变量
const reportData = ref({
  rockyNatureData: [],
  perimeterRockData: [],
  geologicAnalysisReport: [],
  strataDistributionData: { data: [], 极坚固: 25, 很坚固: 50, 坚固: 70, 比较坚固: 90, 中等坚固: 100, 较软: 130, 空洞: 150 },
  conditionMatrixData: []
})

// 设备文件下拉选择
const deviceFiles = ref([])
const selectedFile = ref('')
const fileLoading = ref(false)
const queryLimit = ref(1000) // 查询数据条数，默认1000条

// 设备算法相关数据
const cleanAlgorithm = ref('未设置')
const analysisAlgorithm = ref('未设置')

// 添加算法选择相关变量
const algorithmDialogVisible = ref(false)
const currentAlgorithmType = ref(0) // 0-清洗算法，1-分析算法
const algorithmList = ref([])
const selectedAlgorithmId = ref(null)
const algorithmLoading = ref(false)
const currentCleanAlgorithmId = ref(null) // 当前应用的清洗算法ID
const currentAnalysisAlgorithmId = ref(null) // 当前应用的分析算法ID
const cancelLoading = ref(false) // 取消应用加载状态
const algorithmTableRef = ref(null) // 算法表格引用

// 设备编辑相关
const editDialogVisible = ref(false)
const editFormRef = ref(null)
const editLoading = ref(false)
const editForm = ref({
  deviceName: '',
  serialNumber: '',
  macAddress: '',
  deviceSecret: ''
})

// 表单验证规则
const editRules = {
  deviceName: [{ required: false, message: '请输入设备名称', trigger: 'blur' }],
  serialNumber: [{ required: false, message: '请输入设备编号', trigger: 'blur' }],
  macAddress: [
    { required: false, message: '请输入MAC地址', trigger: 'blur' },
    {
      pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^([0-9A-Fa-f]{2}){6}$/,
      message: 'MAC地址格式不正确',
      trigger: 'blur'
    }
  ],
  deviceSecret: [
    { required: false, message: '请输入设备密钥', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]{6,10}$/, message: '设备密钥应为6-10位字母数字组合', trigger: 'blur' }
  ]
}

// 搜索条件相关变量
const timeRange = ref([])
const selectedHoleNo = ref('')

// 计算是否选择了文件
const fileSelected = computed(() => !!selectedFile.value)

// 计算是否选择了时间范围
const timeRangeSelected = computed(() => {
  return timeRange.value && timeRange.value.length === 2 && !!timeRange.value[0] && !!timeRange.value[1]
})

// 计算是否选择了孔号
const holeNoSelected = computed(() => !!selectedHoleNo.value)

// 计算是否可以执行搜索
const canSearch = computed(() => {
  return fileSelected.value || (timeRangeSelected.value || holeNoSelected.value)
})

// 处理时间范围变化
const handleTimeRangeChange = () => {
  if (timeRangeSelected.value && selectedFile.value) {
    // 如果选择了时间范围，并且已有文件选择，清空文件选择
    selectedFile.value = ''
    ElMessage.info('已清除文件选择，将使用时间范围进行查询')
  }
}

// 处理孔号变化
const handleHoleNoChange = () => {
  if (holeNoSelected.value && selectedFile.value) {
    // 如果选择了孔号，并且已有文件选择，清空文件选择
    selectedFile.value = ''
    ElMessage.info('已清除文件选择，将使用孔号进行查询')
  }
}

// 根据条件进行查询
const handleSearch = async () => {
  if (fileSelected.value) {
    // 选择了文件，但也输入了时间或孔号并点击了查询按钮
    if (timeRangeSelected.value || holeNoSelected.value) {
      // 清空文件选择
      selectedFile.value = ''
      ElMessage.info('将使用自定义条件查询，已清除文件选择')
      
      // 执行条件查询
      await doConditionSearch()
      return
    }
    
    // 如果只选择了文件，使用文件ID和当前的queryLimit重新查询
    try {
      loading.value = true
      ElMessage.info('使用当前条件重新查询...')
      
      // 使用统一查询接口通过文件ID查询
      const response = await queryDrillingData({
        deviceId: deviceId,
        fileId: selectedFile.value,
        limit: Number(queryLimit.value) // 使用当前设置的数据条数限制
      })

      if (response && response.success) {
        // 直接设置报告数据
        reportData.value = response.data || {}
        // 更新最后的项目名称
        if (reportData.value.generateDrillingData != null && reportData.value.generateDrillingData.length > 0) {
          lastDataTunnelName.value = reportData.value.generateDrillingData[0].tunnelName || ''
        }
      } else {
        const errorMsg = response && response.message ? response.message : '未知错误'
        ElMessage.error(`获取文件原始数据失败: ${errorMsg}`)
      }
    } catch (error) {
      console.error('获取文件原始数据出现异常:', error)
      const errorMessage = error.message || error.response?.data?.message || '未知错误'
      ElMessage.error(`获取文件原始数据失败: ${errorMessage}`)
    } finally {
      loading.value = false
    }
    return
  }

  // 按条件查询
  await doConditionSearch()
}

// 使用防抖的查询函数
const debouncedHandleSearch = debouncedApiCall(handleSearch)

// 执行条件查询
const doConditionSearch = async () => {
  try {
    loading.value = true
    reportData.value = {} // 查询前清空数据

    // 构建查询参数 - 使用统一查询接口
    const params = {
      deviceId: deviceId,
      limit: Number(queryLimit.value) // 添加查询数据条数限制
    }

    // 如果有时间范围，添加时间参数
    if (timeRangeSelected.value) {
      params.startTime = timeRange.value[0]
      params.endTime = timeRange.value[1]
    }

    // 如果有孔号，添加孔号参数
    if (holeNoSelected.value) {
      params.holeNo = selectedHoleNo.value
    }

    // 如果没有任何查询条件，提示用户
    if (!params.startTime && !params.endTime && !params.holeNo) {
      ElMessage.warning('请选择至少一个查询条件')
      loading.value = false
      return
    }

    // 调用统一查询接口
    const response = await queryDrillingData(params)

    if (response && response.success) {
      // 直接设置报告数据
      reportData.value = response.data || {}
      // 更新最后的项目名称
      if (reportData.value.generateDrillingData != null && reportData.value.generateDrillingData.length > 0) {
          lastDataTunnelName.value = reportData.value.generateDrillingData[0].tunnelName || ''
      }
    } else {
      ElMessage.error('获取原始数据失败: ' + (response?.message || '未知错误'))
    }
  } catch (error) {
    console.error('数据查询失败:', error)
    ElMessage.error('数据查询失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 刷新数据 - 根据当前查询条件重新获取数据
const refreshData = async () => {
  try {
    ElMessage.info('正在刷新数据...')

    if (fileSelected.value) {
      // 如果选择了文件，重新查询文件数据
      const response = await queryDrillingData({
        deviceId: deviceId,
        fileId: selectedFile.value,
        limit: Number(queryLimit.value)
      })

      if (response && response.success) {
        reportData.value = response.data || {}
        // 更新最后的项目名称
        if (reportData.value.generateDrillingData != null && reportData.value.generateDrillingData.length > 0) {
          lastDataTunnelName.value = reportData.value.generateDrillingData[0].tunnelName || ''
        }
      } else {
        const errorMsg = response && response.message ? response.message : '未知错误'
        ElMessage.error(`刷新数据失败: ${errorMsg}`)
      }
    } else if (timeRangeSelected.value || holeNoSelected.value) {
      // 如果有时间范围或孔号条件，执行条件查询
      await doConditionSearch()
    } else {
      // 没有查询条件时，提示用户
      ElMessage.warning('请先选择查询条件')
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败: ' + (error.message || '未知错误'))
  }
}



// 获取设备详情
const fetchDeviceDetail = async () => {
  try {
    loading.value = true
    const response = await getDeviceDetail(deviceId)

    if (response.success) {
      deviceDetail.value = response.data
      deviceName.value = deviceDetail.value.deviceName

      // 获取设备关联的算法
      await fetchDeviceAlgorithms()

      // 获取设备文件列表
      await fetchDeviceFiles()
    } else {
      ElMessage.error('获取设备详情失败')
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    ElMessage.error('获取设备详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取设备关联的算法
const fetchDeviceAlgorithms = async () => {
  try {
    // 先重置所有算法状态
    cleanAlgorithm.value = '未设置'
    analysisAlgorithm.value = '未设置'
    currentCleanAlgorithmId.value = null
    currentAnalysisAlgorithmId.value = null

    const response = await getDeviceArithmetic(deviceId)

    if (response.success && response.data && response.data.length > 0) {
      // 遍历算法列表，设置对应的算法名称
      response.data.forEach(algorithm => {
        if (algorithm.arithmeticType === 0) {
          // 清洗算法
          cleanAlgorithm.value = algorithm.arithmeticName || '未设置'
          currentCleanAlgorithmId.value = algorithm.arithmeticId
        } else if (algorithm.arithmeticType === 1) {
          // 分析算法（控件算法）
          analysisAlgorithm.value = algorithm.arithmeticName || '未设置'
          currentAnalysisAlgorithmId.value = algorithm.arithmeticId
        }
      })
    }
  } catch (error) {
    console.error('获取设备算法失败:', error)
    // 失败时不显示错误消息，保持默认值"未设置"
  }
}

// 获取设备文件列表
const fetchDeviceFiles = async () => {
  try {
    fileLoading.value = true
    const response = await fileApi.getDeviceFiles(deviceId)

    if (response.success) {
      deviceFiles.value = response.data
      if (deviceFiles.value.length > 0) {
        selectedFile.value = deviceFiles.value[0].id
        // 获取默认选择文件的原始数据
        await handleFileChange(selectedFile.value)
      }
    } else {
      ElMessage.error('获取设备文件列表失败')
    }
  } catch (error) {
    console.error('获取设备文件列表失败:', error)
    // 失败时不显示错误消息
  } finally {
    fileLoading.value = false
  }
}

// 处理文件选择变化
const handleFileChange = async value => {
  // 如果没有选择文件，则清空数据并返回
  if (!value) {
    reportData.value = {}
    ElMessage.warning('未选择任何文件')
    return
  }

  // 如果选择了文件，清空时间范围和孔号
  if (timeRange.value && timeRange.value.length > 0 || selectedHoleNo.value) {
    timeRange.value = []
    selectedHoleNo.value = ''
    ElMessage.info('已清除时间范围和孔号条件')
  }

  // 查找选中的文件
  const selectedFileObj = deviceFiles.value.find(item => item.id === value)
  if (!selectedFileObj) {
    ElMessage.error(`未找到ID为${value}的文件`)
    return
  }

  // 显示文件信息
  ElMessage.success(`已选择文件: ${selectedFileObj.name}`)

  // 获取文件原始数据
  try {
    loading.value = true
    
    // 使用统一查询接口通过文件ID查询
    const response = await queryDrillingData({
      deviceId: deviceId,
      fileId: value,
      limit: Number(queryLimit.value) // 添加查询数据条数限制
    })

    if (response && response.success) {
      // 直接设置报告数据
      reportData.value = response.data || {}
      // 更新最后的项目名称
      if (reportData.value.generateDrillingData != null && reportData.value.generateDrillingData.length > 0) {
          lastDataTunnelName.value = reportData.value.generateDrillingData[0].tunnelName || ''
      }
    } else {
      reportData.value = {} // 清空数据
      const errorMsg = response && response.message ? response.message : '未知错误'
      console.error('获取文件原始数据失败:', errorMsg)
      ElMessage.error(`获取文件原始数据失败: ${errorMsg}`)
    }
  } catch (error) {
    console.error('获取文件原始数据出现异常:', error)
    
    // 详细记录错误信息
    if (error.response) {
      console.error('服务器响应:', error.response.status, error.response.data)
    }
    
    const errorMessage = error.message || error.response?.data?.message || '未知错误'
    ElMessage.error(`获取文件原始数据失败: ${errorMessage}`)

    reportData.value = {} // 出错时清空数据
  } finally {
    loading.value = false
  }
  
  // 如果有文件路径，可以提供下载选项
  if (selectedFileObj.filePath) {
    // 可以在这里实现文件下载或预览功能
  }
}

// 下载算法
const downloadAlgorithm = async algorithm => {
  try {
    ElMessage.info(`正在处理算法: ${algorithm.name}，请稍候...`)

    // 调用下载API
    const response = await downloadArithmetic(algorithm.id, deviceId)

    if (response.success && response.data) {
      // 创建Blob对象
      const blob = new Blob([response.data.encryptedContent], { type: 'application/octet-stream' })

      // 创建文件名：算法名称_算法类型.alg
      const algorithmType = response.data.type === 0 ? 'cleaning' : 'analysis'
      const fileName = `${response.data.name}_${algorithmType}.alg`

      // 创建下载链接
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = fileName
      link.style.display = 'none'

      // 添加到DOM中并触发点击
      document.body.appendChild(link)
      link.click()

      // 清理DOM
      setTimeout(() => {
        URL.revokeObjectURL(link.href)
        document.body.removeChild(link)
      }, 100)

      ElMessage.success(`算法 "${algorithm.name}" 下载成功`)
    } else {
      ElMessage.error('获取算法内容失败')
    }
  } catch (error) {
    console.error('下载算法失败:', error)
    ElMessage.error(`下载算法失败: ${error.message || '未知错误'}`)
  }
}



// 打开算法选择对话框
const openAlgorithmDialog = type => {
  currentAlgorithmType.value = type
  algorithmDialogVisible.value = true
  fetchAvailableAlgorithms()
}

// 获取可用算法列表
const fetchAvailableAlgorithms = async () => {
  try {
    algorithmLoading.value = true
    const params = {
      type: currentAlgorithmType.value,
      page: 1,
      pageSize: 100
    }

    // 如果是分析算法（控件算法），需要按product_key筛选
    if (currentAlgorithmType.value === 1 && deviceDetail.value?.productKey) {
      params.product_key = deviceDetail.value.productKey
    }

    const response = await getArithmeticList(params)

    if (response.success) {
      algorithmList.value = response.data.list || []

      // 获取算法列表后，自动选中已应用的算法
      const currentAppliedAlgorithmId = currentAlgorithmType.value === 0
        ? currentCleanAlgorithmId.value
        : currentAnalysisAlgorithmId.value

      if (currentAppliedAlgorithmId) {
        // 如果有已应用的算法，选中它
        selectedAlgorithmId.value = currentAppliedAlgorithmId

        // 在下一个tick中设置表格当前行，确保表格已经渲染完成
        nextTick(() => {
          if (algorithmTableRef.value) {
            const targetRow = algorithmList.value.find(alg => alg.id === currentAppliedAlgorithmId)
            if (targetRow) {
              algorithmTableRef.value.setCurrentRow(targetRow)
            }
          }
        })
      } else {
        // 如果没有已应用的算法，清空选择
        selectedAlgorithmId.value = null
      }
    } else {
      ElMessage.error('获取算法列表失败')
    }
  } catch (error) {
    console.error('获取算法列表失败:', error)
    ElMessage.error('获取算法列表失败: ' + (error.message || '未知错误'))
  } finally {
    algorithmLoading.value = false
  }
}

// 应用所选算法
const applyAlgorithm = async () => {
  if (!selectedAlgorithmId.value) {
    ElMessage.warning('请选择要应用的算法')
    return
  }

  try {
    algorithmLoading.value = true

    const data = {
      arithmeticId: selectedAlgorithmId.value,
      arithmeticType: currentAlgorithmType.value
    }

    const response = await setDeviceArithmetic(deviceId, data)

    if (response.success) {
      ElMessage.success('算法应用成功')
      algorithmDialogVisible.value = false
      await fetchDeviceAlgorithms() // 重新获取设备算法信息

      // 自动刷新数据
      await refreshData()
    } else {
      ElMessage.error(response.message || '算法应用失败')
    }
  } catch (error) {
    console.error('应用算法失败:', error)
    ElMessage.error('应用算法失败: ' + (error.message || '未知错误'))
  } finally {
    algorithmLoading.value = false
  }
}

// 选择算法
const handleAlgorithmSelect = row => {
  selectedAlgorithmId.value = row ? row.id : null
}

// 计算属性：判断选中的算法是否已应用
const isSelectedAlgorithmApplied = computed(() => {
  if (!selectedAlgorithmId.value) return false

  if (currentAlgorithmType.value === 0) {
    return selectedAlgorithmId.value === currentCleanAlgorithmId.value
  } else {
    return selectedAlgorithmId.value === currentAnalysisAlgorithmId.value
  }
})

// 统一的算法操作处理方法
const handleAlgorithmAction = async () => {
  if (isSelectedAlgorithmApplied.value) {
    // 如果已应用，则取消应用
    await cancelAlgorithm()
  } else {
    // 如果未应用，则应用算法
    await applyAlgorithm()
  }
}

// 取消应用算法
const cancelAlgorithm = async () => {
  try {
    // 找到当前选中的算法信息
    const selectedAlgorithm = algorithmList.value.find(alg => alg.id === selectedAlgorithmId.value)
    const algorithmName = selectedAlgorithm ? selectedAlgorithm.name : '当前算法'

    await ElMessageBox.confirm(
      `确定要取消应用算法"${algorithmName}"吗？`,
      '确认取消应用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cancelLoading.value = true

    // 使用删除接口来取消应用
    const response = await deleteDeviceArithmetic(deviceId, currentAlgorithmType.value)

    if (response.success) {
      ElMessage.success('取消应用成功')
      algorithmDialogVisible.value = false

      // 重新获取设备算法信息，fetchDeviceAlgorithms会自动重置状态
      await fetchDeviceAlgorithms()

      // 自动刷新数据
      await refreshData()
    } else {
      ElMessage.error(response.message || '取消应用失败')
    }
  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作时不显示错误
      console.error('取消应用算法失败:', error)
      ElMessage.error('取消应用失败: ' + (error.message || '未知错误'))
    }
  } finally {
    cancelLoading.value = false
  }
}

// 打开编辑对话框
const openEditDialog = () => {
  editForm.value = {
    deviceName: deviceDetail.value?.deviceName || '',
    serialNumber: deviceDetail.value?.serialNumber || '',
    macAddress: deviceDetail.value?.macAddress || '',
    deviceSecret: deviceDetail.value?.deviceSecret || ''
  }
  editDialogVisible.value = true
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    // 准备提交数据，不包含序列号
    const submitData = {
      deviceName: editForm.value.deviceName,
      macAddress: editForm.value.macAddress,
      deviceSecret: editForm.value.deviceSecret,
      projectName: editForm.value.projectName
      // 不包含serialNumber字段
    }

    editLoading.value = true
    const response = await updateDevice(deviceId, submitData)

    if (response.success) {
      ElMessage.success('设备信息更新成功')
      editDialogVisible.value = false
      // 重新获取设备详情以更新页面
      fetchDeviceDetail()
    } else {
      ElMessage.error(response.message || '设备信息更新失败')
    }
  } catch (error) {
    console.error('更新设备信息失败:', error)
    ElMessage.error('更新设备信息失败: ' + (error.message || '未知错误'))
  } finally {
    editLoading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 注意：hasSearchCriteria 已移除，因为页面加载时会自动选择第一个文件，
// 所以用户几乎总是有查询条件，这个计算属性没有实际意义



// 设备在线状态相关变量
const deviceOnline = ref(false)
const lastDataTime = ref(null)
const lastDataTunnelName = ref(null)

// 获取设备最新数据判断在线状态
const checkDeviceOnlineStatusFromLatest = async () => {
  try {
    const response = await getLatestDeviceData(deviceId)
    
    if (response.success && response.data) {
      // 获取最新的数据记录
      const latestData = response.data
      // 最后一条数据的隧道名称（项目名称）
      lastDataTunnelName.value = latestData.tunnelName || ''
      // 获取采集时间
      const collectionTime = latestData.collectionAt || latestData.collection_at
      
      if (collectionTime) {
        // 保存最后数据时间
        lastDataTime.value = collectionTime
        
        // 计算时间差
        const lastTime = new Date(collectionTime)
        const currentTime = new Date()
        const diffMinutes = (currentTime - lastTime) / (1000 * 60)
        
        // 如果在5分钟内有数据，则认为设备在线
        deviceOnline.value = diffMinutes <= 5
      } else {
        deviceOnline.value = false
      }
    } else {
      deviceOnline.value = false
    }
  } catch (error) {
    console.error('获取设备最新数据失败:', error)
    deviceOnline.value = false
  }
}

// 跳转到实时数据页面
const goToRealtime = () => {
  router.push(`/device/realtime/${route.params.id}`)
}

// 跳转到数字岩芯页面
const goToDigitalCoreView = () => {
  router.push(`/device/digitalcore/${route.params.id}`)
}

// 前往图表配置页面
const goToChartConfig = () => {
  router.push(`/device/chart-config/${deviceId}`)
}

// 处理模版开关切换
const handleTemplateToggle = (value) => {
  console.log('模版开关状态:', value ? '开启' : '关闭')
  // 这里可以添加其他逻辑，比如保存用户偏好设置等
}

// 获取图表配置
const fetchChartConfig = async () => {
  try {
    chartConfigLoading.value = true

    // 使用新的API获取设备已启用的图表组件
    const response = await getDeviceEnabledCharts(deviceId)
    if (!response.success) {
      throw new Error(response.message || '获取图表配置失败')
    }

    // 只返回已启用的图表组件
    availableCharts.value = response.data.list

  } catch (error) {
    console.error('获取图表配置失败:', error)
    ElMessage.error('获取图表配置失败: ' + (error.message || '未知错误'))
  } finally {
    chartConfigLoading.value = false
  }
}

// 根据数据显示模式构建卡钻算法的functions参数
const buildFunctionsForStuck = (dataDisplayMode) => {
  if (dataDisplayMode === 'original') {
    // 原始模式：直接对原始数据进行卡钻算法
    return 'generateDetectStuckEventsData'
  } else {
    // 平滑模式或全部模式：先进行滤波，再基于滤波数据进行卡钻算法
    return 'generateDrillingCurveFilteringData,generateDetectStuckEventsData'
  }
}

// 根据数据显示模式构建突进算法的functions参数
const buildFunctionsForMutation = (dataDisplayMode) => {
  if (dataDisplayMode === 'original') {
    // 原始模式：直接对原始数据进行突进算法
    return 'generateDrillingSpeedMutationData'
  } else {
    // 平滑模式或全部模式：先进行滤波，再基于滤波数据进行突进算法
    return 'generateDrillingCurveFilteringData,generateDrillingSpeedMutationData'
  }
}

// 根据数据显示模式获取卡钻数据存储字段名
const getStuckDataKey = (dataDisplayMode) => {
  const isFilteredMode = dataDisplayMode === 'filtered' || dataDisplayMode === 'both'
  return isFilteredMode ? 'generateCurveFilterDetectStuckEventsData' : 'generateDetectStuckEventsData'
}

// 根据数据显示模式获取突进数据存储字段名
const getMutationDataKey = (dataDisplayMode) => {
  const isFilteredMode = dataDisplayMode === 'filtered' || dataDisplayMode === 'both'
  return isFilteredMode ? 'generateCurveFilterDrillingSpeedMutationData' : 'generateDrillingSpeedMutationData'
}

// 处理卡钻开关切换事件
const handleStuckToggle = async (event) => {
  if (!event.enabled) {
    // 开关关闭时清除卡钻数据，确保下次开启时能检测到数据变化
    // 清除所有可能的卡钻数据字段
    reportData.value.generateDetectStuckEventsData = {
      stuckPoints: [],
      _clearDataTimestamp: Date.now() // 添加清除时间戳
    }
    reportData.value.generateCurveFilterDetectStuckEventsData = {
      stuckPoints: [],
      _clearDataTimestamp: Date.now() // 添加清除时间戳
    }
    return
  }

  try {
    // 根据数据显示模式构建functions参数
    const functions = buildFunctionsForStuck(event.dataDisplayMode || 'original')

    // 构建查询参数，使用当前的查询条件
    const params = {
      deviceId: deviceId,
      functions: functions,
      limit: Number(queryLimit.value)
    }

    // 如果有文件选择，使用文件ID
    if (selectedFile.value) {
      params.fileId = selectedFile.value
    } else {
      // 如果有时间范围，添加时间参数
      if (timeRangeSelected.value) {
        params.startTime = timeRange.value[0]
        params.endTime = timeRange.value[1]
      }

      // 如果有孔号，添加孔号参数
      if (holeNoSelected.value) {
        params.holeNo = selectedHoleNo.value
      }
    }

    // 调用新的算法接口
    const response = await queryDataWithAlgorithms(params)

    if (response && response.success) {
      // 使用公共函数获取数据存储字段名
      const stuckDataKey = getStuckDataKey(event.dataDisplayMode || 'original')

      // 更新报告数据中的卡钻检测结果
      if (response.data && response.data.generateDetectStuckEventsData &&
          response.data.generateDetectStuckEventsData.stuckPoints &&
          response.data.generateDetectStuckEventsData.stuckPoints.length > 0) {
        // 将卡钻检测结果添加到报告数据中
        reportData.value[stuckDataKey] = response.data.generateDetectStuckEventsData
        ElMessage.success(`卡钻数据加载成功，检测到 ${response.data.generateDetectStuckEventsData.stuckPoints.length} 个卡钻事件`)
      } else {
        // 接口成功但返回空数据
        reportData.value[stuckDataKey] = {
          stuckPoints: [],
          _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
        }
        ElMessage.warning('当前查询条件下未检测到卡钻事件')
      }
    } else {
      const errorMsg = response?.message || '未知错误'
      ElMessage.error(`卡钻算法执行失败: ${errorMsg}`)

      // 使用公共函数获取数据存储字段名
      const stuckDataKey = getStuckDataKey(event.dataDisplayMode || 'original')

      // 设置空数据，让组件自己判断和处理
      reportData.value[stuckDataKey] = {
        stuckPoints: [],
        _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
      }
    }
  } catch (error) {
    console.error('调用卡钻算法接口失败:', error)
    ElMessage.error('调用卡钻算法接口失败: ' + (error.message || '未知错误'))

    // 使用公共函数获取数据存储字段名
    const stuckDataKey = getStuckDataKey(event.dataDisplayMode || 'original')

    // 设置空数据，让组件自己判断和处理（触发开关自动关闭）
    reportData.value[stuckDataKey] = {
      stuckPoints: [],
      _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
    }
  }
}

// 处理突进开关切换事件
const handleMutationToggle = async (event) => {
  if (!event.enabled) {
    // 开关关闭时清除突进数据，确保下次开启时能检测到数据变化
    // 清除所有可能的突进数据字段
    reportData.value.generateDrillingSpeedMutationData = {
      upwardPoints: [],
      downwardPoints: [],
      _clearDataTimestamp: Date.now() // 添加清除时间戳
    }
    reportData.value.generateCurveFilterDrillingSpeedMutationData = {
      upwardPoints: [],
      downwardPoints: [],
      _clearDataTimestamp: Date.now() // 添加清除时间戳
    }
    return
  }

  try {
    // 根据数据显示模式构建functions参数
    const functions = buildFunctionsForMutation(event.dataDisplayMode || 'original')

    // 构建查询参数，使用当前的查询条件
    const params = {
      deviceId: deviceId,
      functions: functions,
      limit: Number(queryLimit.value)
    }

    // 如果有文件选择，使用文件ID
    if (selectedFile.value) {
      params.fileId = selectedFile.value
    } else {
      // 如果有时间范围，添加时间参数
      if (timeRangeSelected.value) {
        params.startTime = timeRange.value[0]
        params.endTime = timeRange.value[1]
      }

      // 如果有孔号，添加孔号参数
      if (holeNoSelected.value) {
        params.holeNo = selectedHoleNo.value
      }
    }

    // 调用新的算法接口
    const response = await queryDataWithAlgorithms(params)

    if (response && response.success) {
      // 使用公共函数获取数据存储字段名
      const mutationDataKey = getMutationDataKey(event.dataDisplayMode || 'original')

      // 更新报告数据中的突进检测结果
      if (response.data && response.data.generateDrillingSpeedMutationData) {
        const mutationData = response.data.generateDrillingSpeedMutationData
        const hasUpData = mutationData.upwardPoints && mutationData.upwardPoints.length > 0
        const hasDownData = mutationData.downwardPoints && mutationData.downwardPoints.length > 0

        if (hasUpData || hasDownData) {
          // 将突进检测结果添加到报告数据中
          reportData.value[mutationDataKey] = mutationData
          const upCount = mutationData.upwardPoints?.length || 0
          const downCount = mutationData.downwardPoints?.length || 0
          ElMessage.success(`突进数据加载成功，检测到 ${upCount} 个上突进事件，${downCount} 个下突进事件`)
        } else {
          // 接口成功但返回空数据
          reportData.value[mutationDataKey] = {
            upwardPoints: [],
            downwardPoints: [],
            _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
          }
          ElMessage.warning('当前查询条件下未检测到突进事件')
        }
      } else {
        // 设置空数据
        reportData.value[mutationDataKey] = {
          upwardPoints: [],
          downwardPoints: [],
          _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
        }
        ElMessage.warning('当前查询条件下未检测到突进事件')
      }
    } else {
      const errorMsg = response?.message || '未知错误'
      ElMessage.error(`突进算法执行失败: ${errorMsg}`)

      // 使用公共函数获取数据存储字段名
      const mutationDataKey = getMutationDataKey(event.dataDisplayMode || 'original')

      // 设置空数据，让组件自己判断和处理
      reportData.value[mutationDataKey] = {
        upwardPoints: [],
        downwardPoints: [],
        _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
      }
    }
  } catch (error) {
    console.error('调用突进算法接口失败:', error)
    ElMessage.error('调用突进算法接口失败: ' + (error.message || '未知错误'))

    // 使用公共函数获取数据存储字段名
    const mutationDataKey = getMutationDataKey(event.dataDisplayMode || 'original')

    // 设置空数据，让组件自己判断和处理（触发开关自动关闭）
    reportData.value[mutationDataKey] = {
      upwardPoints: [],
      downwardPoints: [],
      _emptyDataTimestamp: Date.now() // 添加时间戳强制触发数据变化
    }
  }
}



// 图表配置相关
const availableCharts = ref([])
const chartConfigLoading = ref(false)

// 模版开关相关
const useTemplate = ref(false) // 默认关闭
const templateLoading = ref(false)

// 组件挂载后初始化
onMounted(() => {
  // 获取设备详情
  fetchDeviceDetail()
  
  // 获取设备最新数据判断在线状态
  checkDeviceOnlineStatusFromLatest()
  
  // 获取设备图表配置
  fetchChartConfig()
  
  // 定时更新设备状态
  const statusInterval = setInterval(() => {
    checkDeviceOnlineStatusFromLatest()
  }, 60000) // 每分钟检查一次
  
  // 组件卸载时清除定时器
  onBeforeUnmount(() => {
    clearInterval(statusInterval)
  })
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 不再需要清理图表实例，因为这些都在组件内部处理了
})
</script>

<style scoped>
.device-detail-container {
  padding: 20px;
  background-color: var(--apple-background);
}

.device-header-info {
  display: flex;
  align-items: center;
}

.device-name {
  font-size: 18px;
  font-weight: bold;
  margin-right: 16px;
}

.device-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

/* 顶部信息栏 */
.top-info-bar {
  margin: 20px 0;
  border-radius: 8px;
  background: var(--apple-card);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  position: relative;
  max-width: 100%;
}

.device-info-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  margin-right: 20px;
  max-width: calc(100% - 350px);
}

.info-content {
  display: flex;
  width: 100%;
  align-items: center;
}

.info-item-row {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.info-items {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-right: 10px;
  flex: 1;
}

.info-item {
  flex: 0 0 auto;
  margin: 0 20px;
  display: flex;
  flex-direction: column;
  justify-items: center;
  align-items: center;
  text-align: center;
}

.info-item .label {
  font-weight: 700;
  font-size: 16px;
  color: var(--apple-text-primary);
  margin-bottom: 4px;
}

.info-item .value {
  font-size: 12px;
  color: var(--apple-text-secondary);
}

.algorithms-section {
  display: flex;
  border-left: 1px solid var(--apple-border);
  padding-left: 20px;
  align-items: center;
  justify-content: center;
  min-width: 320px;
  flex: 0 0 auto;
}

.algo-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100%;
}

.algorithm-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  margin-right: 15px;
  border: 1px solid var(--apple-border);
  min-width: 150px;
  height: 100%;
}

.algorithm-item:last-child {
  margin-right: 0;
}

.algorithm-item:hover {
  background-color: #f0f8ff;
  border-color: #d9ecff;
}

.algo-info {
  display: flex;
  flex-direction: column;
}

.algo-name {
  font-weight: 600;
  color: var(--apple-primary);
  margin-bottom: 4px;
}

.algo-title {
  font-size: 13px;
  color: var(--apple-text-secondary);
}

/* 卡片通用样式 - 使用全局样式，仅保留局部特定样式 */
.el-card {
  margin-bottom: 20px;
}

.card-header {
  font-size: 28px;
  font-weight: 500;
  color: var(--apple-text-primary);
  display: flex;
  align-items: center;
  justify-content: start;
  border-bottom: none;
}

.header-right {
  display: flex;
  align-items: center;
  margin-left: auto;
  flex-wrap: wrap;
  gap: 10px;
}

/* 分析报告区域样式 */
.report-section {
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

.report-section :deep(.el-card__body) {
  max-width: 100% !important;
  overflow: hidden !important;
}

.report-section :deep(.el-card__header) {
  box-sizing: border-box !important;
}

/* 分析报告相关样式已移至 AnalysisReportDisplay 组件 */

/* 算法对话框样式 */
.algorithm-dialog-content {
  min-height: 300px;
}

.no-algorithm {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--apple-text-secondary);
  font-size: 14px;
}

.form-item-tip {
  font-size: 12px;
  color: var(--apple-text-secondary);
  line-height: 1;
  margin-top: 4px;
}

.edit-btn {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.device-status {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.status-tag {
  margin-right: 5px;
}

.offline-time {
  font-size: 12px;
  color: var(--apple-text-secondary);
}
</style>
