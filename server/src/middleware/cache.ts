import { Request, Response, NextFunction } from 'express'
import { AuthRequest } from '../types/middleware'
import crypto from 'crypto'
import config from '../config'
import { logger } from './logger'

// 缓存锁机制 - 防止缓存雪崩
const cacheLocks = new Map<string, Promise<any>>()

// 定期清理过期的缓存锁（防止内存泄漏）
setInterval(() => {
  const now = Date.now()
  for (const [key, promise] of cacheLocks.entries()) {
    // 如果锁超过30秒还没释放，强制清理
    const lockTime = (promise as any)._lockTime || now
    if (now - lockTime > 30000) {
      console.warn(`强制清理过期缓存锁: ${key}`)
      cacheLocks.delete(key)
    }
  }
}, 30000) // 每30秒清理一次

/**
 * 通用缓存中间件
 * 支持自动缓存键生成、用户隔离、条件缓存和响应拦截
 * @param category 缓存类别
 * @param ttl 过期时间(秒)，可选
 * @returns Express中间件函数
 */
export const cacheMiddleware = (category: string, ttl?: number) => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    const userId = req.user?.id

    // 如果没有用户信息或缓存系统未初始化，跳过缓存
    if (!userId || !global.cacheManager) {
      return next()
    }

    try {
      // 优化：生成更简单的缓存键标识符，减少序列化开销
      const queryString = new URLSearchParams(req.query as any).toString()
      const bodyString = req.body ? JSON.stringify(req.body) : ''
      const requestSignature = `${req.path}?${queryString}${bodyString}`

      const paramsHash = crypto.createHash('md5')
        .update(requestSignature)
        .digest('hex')
        .substring(0, 8)

      const cacheKey = global.cacheManager.generateKey(category, userId, paramsHash)
      
      const requestId = Math.random().toString(36).substring(2, 11)
      const startTime = Date.now()

      // 检查缓存
      const cacheCheckStart = Date.now()
      let cached = await global.cacheManager.get(cacheKey)
      const cacheCheckTime = Date.now() - cacheCheckStart

      if (cached) {
        const responseTime = Date.now() - startTime
        logger.info(`🎯 [${requestId}] 缓存命中: ${cacheKey} | 缓存查询: ${cacheCheckTime}ms | 总响应: ${responseTime}ms`)

        // 优化：直接修改cached对象，避免对象展开的性能开销
        const jsonStart = Date.now()
        if (typeof cached === 'object' && cached !== null) {
          cached.fromCache = true
        }
        const result = res.json(cached)
        const jsonTime = Date.now() - jsonStart

        if (responseTime > 50) {
          logger.warn(`⚠️ [${requestId}] 缓存命中但响应较慢: 总时间${responseTime}ms (缓存查询:${cacheCheckTime}ms, JSON序列化:${jsonTime}ms)`)
        }

        return result
      }

      // 检查是否有其他请求正在处理相同的缓存键（防止缓存雪崩）
      if (cacheLocks.has(cacheKey)) {
        const lockWaitStart = Date.now()
        logger.info(`⏳ [${requestId}] 等待缓存锁: ${cacheKey}`)
        try {
          // 等待其他请求完成并再次检查缓存（添加超时机制）
          await Promise.race([
            cacheLocks.get(cacheKey),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Lock timeout')), 3000)) // 3秒超时
          ])
          const lockWaitTime = Date.now() - lockWaitStart
          cached = await global.cacheManager.get(cacheKey)
          if (cached) {
            const totalResponseTime = Date.now() - startTime
            logger.info(`🔓 [${requestId}] 缓存锁等待后命中: ${cacheKey} | 锁等待: ${lockWaitTime}ms | 总响应: ${totalResponseTime}ms`)
            // 优化：直接修改cached对象，避免对象展开的性能开销
            if (typeof cached === 'object' && cached !== null) {
              cached.fromCache = true
            }
            return res.json(cached)
          }
        } catch (error) {
          const lockWaitTime = Date.now() - lockWaitStart
          logger.error(`❌ [${requestId}] 缓存锁等待失败: ${cacheKey} | 等待时间: ${lockWaitTime}ms`, error)
          // 继续执行正常流程
        }
      }

      logger.info(`💾 [${requestId}] 缓存未命中，开始处理: ${cacheKey}`)

      // 创建缓存锁
      let lockResolve: (value: any) => void
      let lockReject: (reason?: any) => void
      const lockPromise = new Promise((resolve, reject) => {
        lockResolve = resolve
        lockReject = reject
      })
      // 添加锁创建时间，用于清理过期锁
      ;(lockPromise as any)._lockTime = Date.now()
      cacheLocks.set(cacheKey, lockPromise)

      // 拦截响应，在成功响应时设置缓存
      const originalJson = res.json
      res.json = function(data) {
        try {
          const totalResponseTime = Date.now() - startTime
          // 只缓存成功的响应
          if (data && data.success) {
            global.cacheManager.set(cacheKey, data, ttl)
            logger.info(`✅ [${requestId}] 缓存已设置: ${cacheKey} | TTL: ${ttl || 'default'}秒 | 总响应: ${totalResponseTime}ms`)
            lockResolve(data) // 释放缓存锁
          } else {
            logger.info(`⚠️ [${requestId}] 响应失败，未缓存: ${cacheKey} | 总响应: ${totalResponseTime}ms`)
            lockResolve(null) // 即使失败也要释放锁
          }
        } catch (error) {
          const totalResponseTime = Date.now() - startTime
          logger.error(`❌ [${requestId}] 缓存设置失败: ${cacheKey} | 总响应: ${totalResponseTime}ms`, error)
          lockReject(error)
        } finally {
          // 清理缓存锁
          cacheLocks.delete(cacheKey)
        }
        return originalJson.call(this, data)
      }

      // 处理请求被中断的情况
      req.on('close', () => {
        if (cacheLocks.has(cacheKey)) {
          const totalResponseTime = Date.now() - startTime
          logger.info(`🚫 [${requestId}] 请求中断，清理缓存锁: ${cacheKey} | 运行时间: ${totalResponseTime}ms`)
          lockReject(new Error('Request aborted'))
          cacheLocks.delete(cacheKey)
        }
      })
    } catch (error) {
      logger.error('缓存中间件错误:', error)
      // 缓存失败不应该影响正常业务流程
    }
    
    next()
  }
}

/**
 * 预定义的缓存中间件实例
 */

// 设备缓存中间件 - 5分钟TTL
export const deviceCacheMiddleware = cacheMiddleware('device', config.cache?.deviceTTL || 300)

// 算法缓存中间件 - 30分钟TTL  
export const algorithmCacheMiddleware = cacheMiddleware('algorithm', config.cache?.algorithmTTL || 1800)

// 模板缓存中间件 - 1小时TTL
export const templateCacheMiddleware = cacheMiddleware('template', config.cache?.templateTTL || 3600)

// 字段映射缓存中间件 - 1小时TTL（用于相对静态的配置数据）
export const fieldMappingCacheMiddleware = cacheMiddleware('field_mapping', config.cache?.templateTTL || 3600)

// 文件缓存中间件 - 1分钟TTL（文件列表相对稳定，但会有新增）
export const fileCacheMiddleware = cacheMiddleware('file', config.cache?.fileTTL || 60)

// 导入缓存中间件 - 1分钟TTL（导入数据变化较频繁）
export const importCacheMiddleware = cacheMiddleware('import', config.cache?.importTTL || 60)

/**
 * 创建自定义缓存中间件
 * @param category 缓存类别
 * @param ttl 过期时间(秒)
 * @returns 缓存中间件函数
 */
export const createCacheMiddleware = (category: string, ttl: number) => {
  return cacheMiddleware(category, ttl)
}

/**
 * 缓存跳过中间件
 * 用于在特定条件下跳过缓存
 * @param condition 跳过条件函数
 * @returns Express中间件函数
 */
export const skipCacheMiddleware = (condition: (req: AuthRequest) => boolean) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (condition(req)) {
      // 设置跳过缓存标记
      req.skipCache = true
    }
    next()
  }
}

/**
 * 条件缓存中间件
 * 只在满足特定条件时启用缓存
 * @param category 缓存类别
 * @param ttl 过期时间(秒)
 * @param condition 启用缓存的条件函数
 * @returns Express中间件函数
 */
export const conditionalCacheMiddleware = (
  category: string, 
  ttl: number, 
  condition: (req: AuthRequest) => boolean
) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (condition(req) && !req.skipCache) {
      return cacheMiddleware(category, ttl)(req, res, next)
    }
    next()
  }
}
